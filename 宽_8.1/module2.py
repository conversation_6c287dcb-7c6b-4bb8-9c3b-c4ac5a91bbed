# -*- coding: utf-8 -*-
"""
商机分析模块 - 确保统一使用UTF-8编码
"""
import re
import os
import asyncio
import atexit
import time
from typing import List, Dict, Any, Tuple
from pathlib import Path
import aiofiles
import aiohttp
from datetime import datetime
from autogen_core.memory import Memory, MemoryContent, MemoryMimeType
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.agents.web_surfer import MultimodalWebSurfer
from autogen_agentchat.ui import Console
from autogen_ext.memory.chromadb import ChromaDBVectorMemory, PersistentChromaDBVectorMemoryConfig
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.teams import MagenticOneGroup<PERSON>hat
from autogen_agentchat.tools import AgentTool
from contextlib import asynccontextmanager
from autogen_core import CancellationToken
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from dotenv import load_dotenv
import warnings
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_core.models import Chat<PERSON>ompletionClient, ModelInfo, ModelCapabilities
import sys
import io
import gradio as gr
import traceback
import json
import socket
import subprocess
import platform
import threading

# Suppress all warnings that might interfere with clean output
warnings.filterwarnings("ignore", category=ResourceWarning)
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*not found. Using.*encoding.*")
warnings.filterwarnings("ignore", message=".*Model.*not found.*")
warnings.filterwarnings("ignore", message=".*Using.*encoding.*")
warnings.filterwarnings("ignore", message=".*Finish reason mismatch.*")
warnings.filterwarnings("ignore", message=".*cl100k_base.*")
warnings.filterwarnings("ignore", message=".*grok-4-0709.*")

# Set environment variables to suppress additional warnings
import os

os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["PYTHONWARNINGS"] = "ignore"

# Simple cleanup flag to prevent multiple cleanup attempts
_cleanup_done = False

# 全局变量用于实时日志显示
_current_real_time_log = ""
_log_update_lock = threading.Lock()
_analysis_status = {"running": False, "progress": "", "stage": ""}


def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        # 方法1：连接到外部地址获取本机IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            return local_ip
    except Exception:
        try:
            # 方法2：获取主机名对应的IP
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            if local_ip.startswith("127."):
                # 如果是回环地址，尝试其他方法
                return get_network_interfaces()
            return local_ip
        except Exception:
            return get_network_interfaces()


def get_network_interfaces():
    """获取网络接口IP地址"""
    try:
        if platform.system() == "Windows":
            # Windows系统使用ipconfig
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')
            for i, line in enumerate(lines):
                if 'IPv4' in line and '192.168.' in line:
                    ip = line.split(':')[-1].strip()
                    return ip
                elif 'IPv4' in line and '10.' in line:
                    ip = line.split(':')[-1].strip()
                    return ip
                elif 'IPv4' in line and '172.' in line:
                    ip = line.split(':')[-1].strip()
                    return ip
        else:
            # Linux/Mac系统使用ifconfig或ip命令
            try:
                result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
                ips = result.stdout.strip().split()
                for ip in ips:
                    if ip.startswith(('192.168.', '10.', '172.')):
                        return ip
            except:
                pass

        return "127.0.0.1"  # 默认返回本地地址
    except Exception:
        return "127.0.0.1"


def get_network_info():
    """获取网络访问信息"""
    local_ip = get_local_ip()
    return {
        'local_ip': local_ip,
        'localhost_url': 'http://127.0.0.1:7860',
        'network_url': f'http://{local_ip}:7860' if local_ip != "127.0.0.1" else None,
        'is_network_accessible': local_ip != "127.0.0.1"
    }


def display_all_access_urls(network_info, gradio_share_url=None):
    """显示所有可用的访问URL"""
    print("\n" + "🌐" * 20)
    print("📋 所有可用访问地址汇总")
    print("🌐" * 20)

    print("\n🏠 本地访问:")
    print(f"   ✅ http://127.0.0.1:7860")
    print(f"   ✅ http://localhost:7860")

    if network_info['is_network_accessible']:
        print(f"\n🌐 局域网访问:")
        print(f"   ✅ http://{network_info['local_ip']}:7860")
        print(f"   💡 局域网内其他设备可通过此地址访问")

    if gradio_share_url:
        print(f"\n🌍 公网访问 (Gradio分享):")
        print(f"   ✅ {gradio_share_url}")
        print(f"   💡 全球任何地方都可以通过此链接访问")
    else:
        print(f"\n🌍 公网访问:")
        print(f"   ⏳ Gradio正在生成公网分享链接...")
        print(f"   💡 启动完成后将显示公网访问链接")

    print(f"\n🔧 云服务器访问 (如果部署在云服务器):")
    print(f"   📡 http://[您的公网IP]:7860")
    print(f"   💡 需要在安全组中开放7860端口")

    print(f"\n📱 移动设备访问:")
    print(f"   🏠 本地: http://127.0.0.1:7860")
    if network_info['is_network_accessible']:
        print(f"   🌐 局域网: http://{network_info['local_ip']}:7860")
    if gradio_share_url:
        print(f"   🌍 公网: {gradio_share_url}")

    print("\n" + "🌐" * 20)
    print("💡 提示: 选择最适合您网络环境的访问方式")
    print("🌐" * 20)


class TimingManager:
    """计时管理器，用于统计各阶段耗时"""

    def __init__(self):
        self.start_time = None
        self.stage1_start = None
        self.stage1_end = None
        self.stage2_start = None
        self.stage2_end = None
        self.total_end = None

    def start_total(self):
        """开始总计时"""
        self.start_time = time.time()
        return self.start_time

    def start_stage1(self):
        """开始第一阶段计时"""
        self.stage1_start = time.time()
        return self.stage1_start

    def end_stage1(self):
        """结束第一阶段计时"""
        self.stage1_end = time.time()
        return self.stage1_end

    def start_stage2(self):
        """开始第二阶段计时"""
        self.stage2_start = time.time()
        return self.stage2_start

    def end_stage2(self):
        """结束第二阶段计时"""
        self.stage2_end = time.time()
        return self.stage2_end

    def end_total(self):
        """结束总计时"""
        self.total_end = time.time()
        return self.total_end

    def get_stage1_duration(self):
        """获取第一阶段耗时（秒）"""
        if self.stage1_start and self.stage1_end:
            return self.stage1_end - self.stage1_start
        return 0

    def get_stage2_duration(self):
        """获取第二阶段耗时（秒）"""
        if self.stage2_start and self.stage2_end:
            return self.stage2_end - self.stage2_start
        return 0

    def get_total_duration(self):
        """获取总耗时（秒）"""
        if self.start_time and self.total_end:
            return self.total_end - self.start_time
        return 0

    def format_duration(self, seconds):
        """格式化时间显示"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"

    def get_timing_summary(self):
        """获取计时总结"""
        stage1_duration = self.get_stage1_duration()
        stage2_duration = self.get_stage2_duration()
        total_duration = self.get_total_duration()

        # 避免除零错误
        if total_duration > 0:
            stage1_percent = (stage1_duration / total_duration * 100)
            stage2_percent = (stage2_duration / total_duration * 100)
        else:
            stage1_percent = 0
            stage2_percent = 0

        summary = f"""
=== 执行时间统计 ===
📊 第一阶段（销售专家分析）: {self.format_duration(stage1_duration)}
🚀 第二阶段（智能深度挖掘）: {self.format_duration(stage2_duration)}
⏱️ 总耗时: {self.format_duration(total_duration)}

详细时间分布：
- 第一阶段占比: {stage1_percent:.1f}%
- 第二阶段占比: {stage2_percent:.1f}%
"""
        return summary


class PromptTemplateManager:
    """提示词模板管理器 - 统一的提示词处理规则"""

    def __init__(self):
        self.templates_file = Path("prompt_templates.json")
        self.load_templates()

    def get_unified_processing_rules(self) -> dict:
        """获取统一的处理规则"""
        return {
            "template_priority": "user_template_only",  # 仅使用用户模板
            "fallback_strategy": "strict_error_reporting",  # 失败时直接报错，不使用备用方案
            "task_simplification": "disabled",  # 禁用任务简化
            "prompt_override": "forbidden",  # 禁止覆盖提示词
            "error_handling": "strict_error_reporting",  # 错误时直接报错，保证用户搜索结果高质量
            "validation": "strict_template_compliance"  # 严格遵循模板
        }

    def get_default_sales_expert_template(self) -> str:
        """获取销售专家默认模板"""
        # 从prompt_templates.json文件加载模板
        try:
            if self.templates_file.exists():
                with open(self.templates_file, 'r', encoding='utf-8', errors='ignore') as f:
                    data = json.load(f)
                    template = data.get('sales_expert', "").strip()
                    if template:
                        return template
        except Exception as e:
            print(f"❌ 加载销售专家模板失败: {e}")

        # 如果无法加载模板文件，返回错误信息
        raise ValueError("无法从prompt_templates.json文件加载销售专家模板，请确保文件存在且包含有效的模板")

    def get_default_magentic_one_template(self) -> str:
        """获取MagenticOne默认模板"""
        # 从prompt_templates.json文件加载模板
        try:
            if self.templates_file.exists():
                with open(self.templates_file, 'r', encoding='utf-8', errors='ignore') as f:
                    data = json.load(f)
                    template = data.get('magentic_one', "").strip()
                    if template:
                        return template
        except Exception as e:
            print(f"❌ 加载MagenticOne模板失败: {e}")

        # 如果无法加载模板文件，返回错误信息
        raise ValueError("无法从prompt_templates.json文件加载MagenticOne模板，请确保文件存在且包含有效的模板")

    def load_templates(self):
        """加载用户自定义模板，如果没有则使用默认模板"""
        try:
            if self.templates_file.exists():
                with open(self.templates_file, 'r', encoding='utf-8', errors='ignore') as f:
                    data = json.load(f)
                    sales_expert_custom = data.get('sales_expert', "").strip()
                    magentic_one_custom = data.get('magentic_one', "").strip()

                    # 如果用户模板不为空，使用用户模板
                    if sales_expert_custom:
                        self.sales_expert_template = ensure_utf8_encoding(sales_expert_custom)
                        print("✅ 使用用户自定义销售专家模板")
                    else:
                        # 如果用户模板为空，抛出错误，因为现在必须从文件加载模板
                        raise ValueError("销售专家模板为空，请确保prompt_templates.json文件包含有效的sales_expert模板")

                    if magentic_one_custom:
                        self.magentic_one_template = ensure_utf8_encoding(magentic_one_custom)
                        print("✅ 使用用户自定义MagenticOne模板")
                    else:
                        # 如果用户模板为空，抛出错误，因为现在必须从文件加载模板
                        raise ValueError(
                            "MagenticOne模板为空，请确保prompt_templates.json文件包含有效的magentic_one模板")
            else:
                # 如果没有模板文件，抛出错误
                raise ValueError("未找到prompt_templates.json文件，请确保文件存在且包含有效的模板")
        except Exception as e:
            print(f"❌ 加载模板时出错: {e}")
            raise ValueError(f"无法加载提示词模板: {str(e)}")

    def save_templates(self):
        """保存模板到文件，确保UTF-8编码"""
        try:
            data = {
                'sales_expert': ensure_utf8_encoding(self.sales_expert_template),
                'magentic_one': ensure_utf8_encoding(self.magentic_one_template)
            }
            with open(self.templates_file, 'w', encoding='utf-8', errors='ignore') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存模板时出错: {e}")

    def update_sales_expert_template(self, template: str):
        """更新销售专家模板"""
        self.sales_expert_template = template
        self.save_templates()

    def update_magentic_one_template(self, template: str):
        """更新MagenticOne模板"""
        self.magentic_one_template = template
        self.save_templates()

    def reset_to_default(self):
        """重置为默认模板"""
        # 重新加载模板文件中的内容
        self.load_templates()
        print("✅ 已重新加载prompt_templates.json文件中的模板")

    def format_sales_expert_prompt(self, product_name: str, product_details: str,
                                   countries: str = "", time_period: str = "",
                                   opportunities_count: int = 3) -> str:
        """格式化销售专家提示词"""
        try:
            # 尝试使用所有可能的参数
            return self.sales_expert_template.format(
                product_name=product_name,
                product_details=product_details,
                countries=countries,
                time_period=time_period,
                opportunities_count=opportunities_count
            )
        except KeyError as e:
            # 如果某些参数不存在，只使用基础参数
            try:
                return self.sales_expert_template.format(
                    product_name=product_name,
                    product_details=product_details,
                    countries=countries
                )
            except KeyError:
                # 如果还是失败，抛出错误
                raise ValueError(f"销售专家模板格式化失败: {e}，请检查prompt_templates.json文件中的模板格式")

    def format_magentic_one_prompt(self, product_name: str, product_details: str,
                                   countries: str, time_period: str,
                                   opportunities_count: int, expert_output: str) -> str:
        """格式化MagenticOne提示词"""
        return self.magentic_one_template.format(
            product_name=product_name,
            product_details=product_details,
            countries=countries,
            time_period=time_period,
            opportunities_count=opportunities_count,
            expert_output=expert_output
        )

    def validate_template_usage(self, agent_name: str, user_prompt: str, mode: str = "user_prompt_only") -> dict:
        """验证模板使用是否符合统一处理规则 - 支持纯用户提示词模式"""
        rules = self.get_unified_processing_rules()
        validation_result = {
            "is_valid": True,
            "warnings": [],
            "errors": [],
            "agent_name": agent_name,
            "mode": mode
        }

        if mode == "user_prompt_only":
            # 纯用户提示词模式验证
            validation_result["warnings"].append("使用纯用户提示词模式，已去除系统提示词干扰")

            # 检查是否包含用户模板的关键元素
            if agent_name == "sales_expert":
                expected_keywords = ["professional enterprise sales expert", "Your task is", "Product Information"]
                for keyword in expected_keywords:
                    if keyword.lower() not in user_prompt.lower():
                        validation_result["warnings"].append(f"用户提示词可能缺少关键词: {keyword}")

            elif agent_name == "magentic_one":
                expected_keywords = ["Task Steps", "business opportunity mining", "Final Goal"]
                for keyword in expected_keywords:
                    if keyword.lower() not in user_prompt.lower():
                        validation_result["warnings"].append(f"用户提示词可能缺少关键词: {keyword}")

            # 检查用户提示词长度
            if len(user_prompt) < 100:
                validation_result["warnings"].append("用户提示词较短，建议检查是否完整")

            # 检查是否有旧的系统提示词覆盖迹象（这在新模式下不应该出现）
            old_override_indicators = ["请根据我提供的", "具体需要", "为销售人员找到"]
            for indicator in old_override_indicators:
                if indicator in user_prompt:
                    validation_result["warnings"].append(f"检测到可能的旧式提示词覆盖模式: {indicator}")

        else:
            # 兼容旧的验证模式
            system_message = user_prompt
            task = mode

            # 检查是否使用了完整的模板
            if agent_name == "sales_expert":
                expected_keywords = ["professional enterprise sales expert", "tavily-search", "Website Categories"]
                for keyword in expected_keywords:
                    if keyword.lower() not in system_message.lower():
                        validation_result["warnings"].append(f"缺少关键词: {keyword}")

            elif agent_name == "magentic_one":
                expected_keywords = ["Task Steps", "business opportunity mining", "Final Goal"]
                for keyword in expected_keywords:
                    if keyword.lower() not in system_message.lower():
                        validation_result["warnings"].append(f"缺少关键词: {keyword}")

            # 检查任务是否过于简化
            if len(task) < 50:
                validation_result["warnings"].append("任务描述过于简化，可能违背用户意图")

            # 检查是否有提示词覆盖的迹象
            override_indicators = ["请根据我提供的", "具体需要", "为销售人员找到"]
            for indicator in override_indicators:
                if indicator in task:
                    validation_result["errors"].append(f"检测到提示词覆盖: {indicator}")
                    validation_result["is_valid"] = False

        return validation_result


class SilentLogger:
    """静默日志记录器，用于抑制特定的警告信息"""

    def __init__(self, original_stream):
        self.original_stream = original_stream
        self.buffer = ""

    def write(self, text):
        # 检查是否包含需要抑制的内容
        if self._should_suppress(text):
            return  # 不输出到原始流

        # 输出到原始流
        self.original_stream.write(text)

    def _should_suppress(self, text):
        """检查是否应该抑制这个输出"""
        suppress_patterns = [
            "Model grok-4-0709 not found",
            "Using cl100k_base encoding",
            "not found. Using",
            "encoding.",
            "Finish reason mismatch",
            "UserWarning",
            "DeprecationWarning",
            "ResourceWarning"
        ]

        text_lower = text.lower()
        return any(pattern.lower() in text_lower for pattern in suppress_patterns)

    def flush(self):
        self.original_stream.flush()


class LogCapture:
    """用于捕获控制台输出的类，支持实时日志更新"""

    def __init__(self):
        self.logs = []
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        self.buffer = ""

        # 定义需要过滤的日志模式 - 只过滤真正的技术错误，保留所有分析内容
        self.filter_patterns = [
            r"Model .* not found\. Using .* encoding\.",
            r".*not found\. Using.*encoding.*",
            r".*grok-4-0709.*not found.*",
            r".*cl100k_base.*encoding.*",
            r".*Using cl100k_base encoding.*",
            r"Finish reason mismatch.*",
            r".*DeprecationWarning.*code_executor.*",
            r"Exception ignored in.*",
            r".*unclosed transport.*",
            r".*I/O operation on closed pipe.*",
            r".*ResourceWarning.*",
            r".*ValueError.*I/O operation.*",
            r".*_warn.*unclosed.*"
            # 重要：完全移除对MagenticOne、TextMessage、提示词、搜索结果等内容的过滤
            # 保留所有分析相关的日志内容，包括提示词、执行步骤、中间结果、搜索结果等
            # 特别保留tavily搜索结果和网站推荐内容
        ]

    def _should_filter_line(self, line):
        """检查是否应该过滤这行日志 - 保留提示词和执行流程"""
        import re
        line_stripped = line.strip()

        # 过滤空行
        if not line_stripped:
            return False

        # 保护重要内容：如果包含这些关键词，不要过滤
        important_keywords = [
            'project', '项目', 'company', '公司', 'business opportunity', '商机',
            'analysis result', '分析结果', 'website recommendations', '网站推荐',
            'project name', '项目名称', 'project description', '项目描述',
            'target country', '目标国家', 'opportunities', '机会',
            # 新增：保护提示词和执行流程相关内容
            'prompt', '提示词', 'template', '模板', 'task', '任务',
            'system_message', '系统消息', 'user_message', '用户消息',
            'assistant_message', '助手消息', 'textmessage', 'orchestrator',
            'sales expert', '销售专家', 'magentic', 'websurfer', 'coder',
            'planner', 'executor', 'agent', '代理', 'step', '步骤',
            'round', '轮次', 'iteration', '迭代', 'search', '搜索',
            'tavily', 'browse', '浏览', 'analyze', '分析',
            # 特别保护搜索和网站相关内容
            'website', '网站', 'url', 'http', 'www', '.com', '.org', '.net',
            'search result', '搜索结果', 'found', '找到', 'recommendation', '推荐',
            'vertical field', '垂直领域', 'comprehensive news', '综合新闻',
            'industry information', '行业信息', 'forum', '论坛', 'association', '协会'
        ]

        line_lower = line_stripped.lower()
        for keyword in important_keywords:
            if keyword in line_lower:
                return False  # 不过滤重要内容

        # 检查是否匹配过滤模式
        for pattern in self.filter_patterns:
            if re.search(pattern, line_stripped, re.IGNORECASE):
                return True
        return False

    def write(self, text):
        # 同时写入原始输出和我们的日志
        self.original_stdout.write(text)
        self.original_stdout.flush()  # 立即刷新

        # 添加到缓冲区
        self.buffer += text

        # 如果遇到换行符，将缓冲区内容添加到日志
        if '\n' in text:
            lines = self.buffer.split('\n')
            for line in lines[:-1]:  # 除了最后一个空行
                # 过滤不需要的日志行
                if not self._should_filter_line(line):
                    self.logs.append(line + '\n')

                    # 更新全局实时日志变量 - 完整版本
                    global _current_real_time_log, _log_update_lock
                    try:
                        with _log_update_lock:
                            # 保留完整日志，但限制总长度以提高性能
                            full_log = ''.join(self.logs)
                            # 如果日志过长（超过10000字符），只保留最后8000字符
                            if len(full_log) > 10000:
                                _current_real_time_log = "...\n" + full_log[-8000:]
                            else:
                                _current_real_time_log = full_log
                    except Exception:
                        pass
            self.buffer = lines[-1]  # 保留最后一部分作为新的缓冲区

    def flush(self):
        self.original_stdout.flush()
        # 如果缓冲区还有内容，也添加到日志
        if self.buffer and not self._should_filter_line(self.buffer):
            self.logs.append(self.buffer)
            self.buffer = ""

    def get_full_log(self):
        # 确保缓冲区内容也被包含
        if self.buffer and not self._should_filter_line(self.buffer):
            self.logs.append(self.buffer)
            self.buffer = ""
        return ''.join(self.logs)

    def get_recent_logs(self, lines=50):
        """获取最近的日志行"""
        full_log = self.get_full_log()
        log_lines = full_log.split('\n')
        return '\n'.join(log_lines[-lines:]) if log_lines else ""


def save_logs_to_file(full_log: str, timestamp: str):
    """保存完整日志到文件并提取商机分析结果"""
    try:
        # 创建logs目录
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)

        # 确保UTF-8编码，但保留原始结构用于提取
        raw_log = ensure_utf8_encoding(full_log)

        # 先从原始日志中提取分析结果（在清理之前）
        print("🔍 从原始日志中提取分析结果...")
        analysis_result = extract_orchestrator_content(raw_log, timestamp, logs_dir)

        # 然后清理日志用于显示
        print("🧹 清理日志内容用于显示...")
        cleaned_log = clean_log_content(raw_log)
        cleaned_log = ensure_utf8_encoding(cleaned_log)

        # 保存完整日志
        full_log_file = logs_dir / f"full_log_{timestamp}.txt"
        with open(full_log_file, 'w', encoding='utf-8', errors='ignore') as f:
            f.write(cleaned_log)

        print(f"📁 完整日志已保存到: {full_log_file}")

        # 返回提取的分析结果
        return analysis_result

    except Exception as e:
        print(f"❌ 保存日志时出错: {e}")
        return full_log  # 如果出错，返回原始日志


def clean_expert_output(expert_content: str) -> str:
    """清理销售专家输出，移除错误信息和无用内容，确保UTF-8编码正确处理 - 优化版本"""
    try:
        import re
        import json
        import codecs

        if not expert_content or not expert_content.strip():
            return None

        print(f"🔍 开始清理专家输出，原始长度: {len(expert_content)}")

        # 确保内容是正确的UTF-8编码
        if isinstance(expert_content, bytes):
            expert_content = expert_content.decode('utf-8', errors='ignore')

        # 检查是否包含验证错误 - 特别处理工具参数错误
        if "validation error" in expert_content.lower():
            if "include_domains" in expert_content and "exclude_domains" in expert_content:
                print("❌ 检测到tavily-search工具参数验证错误")
                raise ValueError(
                    "销售专家调用tavily-search工具时参数格式错误。include_domains和exclude_domains应该是数组[]而不是字符串'[]'。请重试。")
            elif len(expert_content.strip()) < 500:
                print("❌ 检测到其他验证错误")
                raise ValueError(f"销售专家分析出现验证错误，请检查提示词模板或输入参数：\n{expert_content}")

        # 首先尝试直接使用原始内容，只做基本清理
        cleaned = expert_content

        # 处理多个JSON对象的情况
        all_extracted_text = []

        # 更全面的JSON模式匹配
        json_patterns = [
            r'\[{\"type\": \"text\", \"text\": \"(.*?)\"\}[^\]]*\]',
            r'{\"type\": \"text\", \"text\": \"(.*?)\"}',
            r'\"text\": \"(.*?)\"'
        ]

        for pattern in json_patterns:
            json_matches = re.findall(pattern, expert_content, re.DOTALL)
            if json_matches:
                print(f"🔍 使用模式 {pattern[:30]}... 找到 {len(json_matches)} 个匹配")

                for i, json_text in enumerate(json_matches):
                    # 解码转义字符
                    decoded_text = json_text.replace('\\n', '\n').replace('\\\"', '"').replace('\\\\', '\\')

                    # 处理Unicode转义序列 - 完整的Unicode解码
                    def decode_unicode(match):
                        try:
                            unicode_value = int(match.group(1), 16)
                            return chr(unicode_value)
                        except (ValueError, OverflowError):
                            return match.group(0)

                    # 解码所有Unicode转义序列
                    decoded_text = re.sub(r'\\u([0-9a-fA-F]{4})', decode_unicode, decoded_text)

                    # 处理可能的双重编码问题
                    try:
                        # 尝试解码可能的字节序列
                        if '\\x' in decoded_text:
                            decoded_text = codecs.decode(decoded_text, 'unicode_escape')
                    except:
                        pass

                    if len(decoded_text.strip()) > 10:  # 进一步降低最小长度要求
                        all_extracted_text.append(decoded_text.strip())
                        print(f"✅ 成功提取第 {i + 1} 个内容，长度: {len(decoded_text)}")
                        # 显示解码后的内容预览（确保UTF-8显示）
                        preview = decoded_text[:100].replace('\n', ' ')
                        print(f"📝 内容预览: {preview}...")

                if all_extracted_text:
                    break  # 如果找到内容就停止尝试其他模式

        # 如果成功提取了JSON内容，使用提取的内容
        if all_extracted_text:
            cleaned = '\n\n'.join(all_extracted_text)
            print(f"✅ 使用JSON提取内容，长度: {len(cleaned)}")
        else:
            print("⚠️ 未找到JSON格式，直接处理原始内容")

            # 尝试直接解码原始内容中的Unicode序列
            def decode_unicode_in_text(match):
                try:
                    return chr(int(match.group(1), 16))
                except:
                    return match.group(0)

            cleaned = re.sub(r'\\u([0-9a-fA-F]{4})', decode_unicode_in_text, expert_content)

        # 定义需要移除的错误模式 - 包含工具调用错误
        error_patterns = [
            r"Timed out while waiting for response.*",
            r"ClientRequest.*Waited \d+\.\d+ seconds\.",
            r"Exception in.*",
            r"Traceback \(most recent call last\):.*",
            r"\[FunctionCall\(.*?\)\]",  # 移除工具调用信息
            r"\[FunctionExecutionResult\(.*?\)\]",  # 移除工具执行结果
            r"Error: Extra data: line \d+ column \d+ \(char \d+\)",  # 移除JSON解析错误
        ]

        # 只移除明确的技术错误信息，保留分析内容
        for pattern in error_patterns:
            before_len = len(cleaned)
            cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE | re.DOTALL)
            if len(cleaned) != before_len:
                print(f"🧹 移除错误模式: {pattern[:30]}...")

        # 更宽松的内容清理，保留更多有用信息
        lines = cleaned.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            # 只跳过明确的技术错误信息行
            if line and not any(error in line.lower() for error in [
                'timed out while waiting', 'clientrequest', 'waited', 'seconds',
                'exception in', 'traceback (most recent'
            ]):
                # 保留所有可能有用的内容，包括网站推荐、分析结果等
                if not (line.startswith('{"type"') and line.endswith('}') and len(line) > 100):
                    # 确保行内容是有效的UTF-8
                    try:
                        line.encode('utf-8')
                        cleaned_lines.append(line)
                    except UnicodeEncodeError:
                        # 如果编码有问题，尝试清理
                        clean_line = line.encode('utf-8', errors='ignore').decode('utf-8')
                        if clean_line.strip():
                            cleaned_lines.append(clean_line)

        result = '\n'.join(cleaned_lines)

        # 进一步清理：移除重复的内容，但保留更多有用信息
        if result:
            # 按段落分割并去重
            paragraphs = result.split('\n\n')
            unique_paragraphs = []
            seen = set()
            for para in paragraphs:
                para_clean = para.strip()
                # 更宽松的长度要求，保留更多内容
                if para_clean and para_clean not in seen and len(para_clean) > 3:
                    unique_paragraphs.append(para_clean)
                    seen.add(para_clean)
            result = '\n\n'.join(unique_paragraphs)

        # 最终清理：移除多余的空行
        result = re.sub(r'\n{3,}', '\n\n', result)
        result = result.strip()

        # 确保最终结果是正确的UTF-8编码
        try:
            result = result.encode('utf-8').decode('utf-8')
        except UnicodeError:
            result = result.encode('utf-8', errors='ignore').decode('utf-8')

        print(f"🎯 清理完成，最终长度: {len(result) if result else 0}")

        # 显示清理后的内容预览（确保UTF-8显示）
        if result:
            preview = result[:200].replace('\n', ' ')
            print(f"📝 清理后内容预览: {preview}...")

        # 更宽松的验证清理结果 - 保留更多有用内容
        if not result or len(result.strip()) < 5:
            print("❌ 清理后内容严重不足")
            raise ValueError(f"销售专家输出清理失败，内容严重不足。原始内容长度: {len(expert_content)}")

        return result

    except Exception as e:
        print(f"❌ 清理专家输出时出错: {e}")
        # 如果清理失败，尝试返回原始内容的基本清理版本
        if expert_content and len(expert_content.strip()) > 10:
            print("🔄 尝试返回基本清理的原始内容")
            basic_cleaned = expert_content.strip()
            # 只移除明显的JSON包装
            basic_cleaned = re.sub(r'^\[{\"type\".*?\"text\":\s*\"', '', basic_cleaned)
            basic_cleaned = re.sub(r'\"\}[^\]]*\]$', '', basic_cleaned)
            basic_cleaned = basic_cleaned.replace('\\n', '\n').replace('\\\"', '"')
            if len(basic_cleaned.strip()) > 5:
                return basic_cleaned

        # 最后才抛出错误
        raise ValueError(f"销售专家输出清理失败: {str(e)}") from e


def ensure_utf8_encoding(text: str) -> str:
    """确保文本是正确的UTF-8编码，解码所有Unicode转义序列"""
    try:
        import re
        import codecs

        if not text:
            return text

        # 处理Unicode转义序列
        def decode_unicode(match):
            try:
                unicode_value = int(match.group(1), 16)
                return chr(unicode_value)
            except (ValueError, OverflowError):
                return match.group(0)

        # 解码所有\uXXXX格式的Unicode转义序列
        decoded_text = re.sub(r'\\u([0-9a-fA-F]{4})', decode_unicode, text)

        # 处理可能的双重编码问题
        try:
            if '\\x' in decoded_text:
                decoded_text = codecs.decode(decoded_text, 'unicode_escape')
        except:
            pass

        # 确保最终结果是有效的UTF-8
        try:
            decoded_text.encode('utf-8')
            return decoded_text
        except UnicodeEncodeError:
            return decoded_text.encode('utf-8', errors='ignore').decode('utf-8')

    except Exception as e:
        print(f"⚠️ UTF-8编码处理出错: {e}")
        return text


def replace_technical_terms(content: str) -> str:
    """替换技术术语为用户友好的描述，但保留重要的分隔标记用于内容提取"""
    import re

    # 定义替换规则 - 注意顺序很重要
    replacements = [
        # 先处理完整的分隔标记，保留原始格式用于提取，但显示友好名称
        (r'---------- TextMessage \(MagenticOneOrchestrator\) ----------', '---------- 🤖 智能分析结果 ----------'),
        # 然后处理其他术语
        (r'MagenticOneOrchestrator', '智能分析协调器'),
        (r'MagenticOne', '智能分析引擎'),
        (r'magentic.*one', '智能分析'),
        (r'autogen', '智能系统'),
        (r'grok-4-0709', 'llama-4-maverick'),
        (r'meta-llama/llama-4-maverick', 'llama-4-maverick'),
        (r'TextMessage \(智能分析协调器\)', 'TextMessage (🤖智能分析协调器)')
    ]

    result = content
    for pattern, replacement in replacements:
        result = re.sub(pattern, replacement, result, flags=re.IGNORECASE)

    return result


def clean_log_content(log_content: str) -> str:
    """清理日志内容，移除多余的符号和格式化"""
    try:
        import re

        # 移除ANSI转义序列
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        cleaned = ansi_escape.sub('', log_content)

        # 替换技术术语
        cleaned = replace_technical_terms(cleaned)

        # 定义需要过滤的日志模式（保留重要的分析结果标记）
        filter_patterns = [
            r"Model .* not found\. Using .* encoding\.",
            r".*not found\. Using.*encoding.*",
            r".*grok-4-0709.*not found.*",
            r".*cl100k_base.*encoding.*",
            r".*Using cl100k_base encoding.*",
            r"Finish reason mismatch.*",
            r".*Instantiating MagenticOne without a code_executor is deprecated.*",
            r".*UserWarning.*Finish reason mismatch.*",
            r".*DeprecationWarning.*code_executor.*",
            r"Exception ignored in.*",
            r".*unclosed transport.*",
            r".*I/O operation on closed pipe.*",
            r".*ResourceWarning.*",
            r".*ValueError: I/O operation on closed pipe.*",
            r".*Traceback \(most recent call last\):.*unclosed.*",
            r".*_warn\(f\"unclosed transport.*",
            r".*BaseSubprocessTransport.*",
            r".*ProactorBasePipeTransport.*",
            r".*fileno.*closed pipe.*"
            # 重要：不过滤包含 "---------- TextMessage (MagenticOneOrchestrator) ----------" 的行
            # 这些是分析结果的重要分隔标记
        ]

        # 按行处理
        lines = cleaned.split('\n')
        cleaned_lines = []
        prev_empty = False

        for line in lines:
            line_stripped = line.strip()

            # 保护提示词和执行流程相关内容
            protected_keywords = [
                '提示词详情', 'prompt', '执行结果详情', 'execution result',
                '销售专家', 'sales expert', 'magentic', 'orchestrator',
                '📝', '📊', '🤖', '📋', '📅', '=' * 80, '-' * 80,
                'system_message', 'user_message', 'assistant_message',
                'textmessage', 'websurfer', 'coder', 'planner'
            ]

            line_lower = line_stripped.lower()
            is_protected = any(keyword.lower() in line_lower for keyword in protected_keywords)

            # 如果是受保护的内容，直接保留
            if is_protected:
                cleaned_lines.append(line_stripped)
                prev_empty = False
                continue

            # 检查是否应该过滤这行
            should_filter = False
            for pattern in filter_patterns:
                if re.search(pattern, line_stripped, re.IGNORECASE):
                    should_filter = True
                    break

            if should_filter:
                continue

            # 处理空行
            if line_stripped == "":
                if not prev_empty:
                    cleaned_lines.append("")
                prev_empty = True
            else:
                cleaned_lines.append(line_stripped)
                prev_empty = False

        return '\n'.join(cleaned_lines)
    except Exception as e:
        print(f"清理日志内容时出错: {e}")
        return log_content


def extract_orchestrator_content(full_log: str, timestamp: str, logs_dir: Path):
    """提取最后一个智能分析结果的内容 - 改进版本"""
    try:
        import re

        print("🔍 开始提取分析结果...")
        print(f"📊 日志总长度: {len(full_log)} 字符")

        # 扩展的分析结果标记模式
        patterns = [
            r"---------- TextMessage \(MagenticOneOrchestrator\) ----------",  # 原始标记
            r"---------- 智能分析结果 ----------",  # 替换后的标记
            r"---------- TextMessage \(智能分析协调器\) ----------",  # 另一种替换形式
            r"TextMessage.*MagenticOneOrchestrator",  # 更宽松的匹配
            r"Project \d+:",  # 直接查找项目格式
            r"项目\d+:",  # 中文项目格式
            r"商机分析结果",  # 商机分析标记
            r"业务机会挖掘",  # 业务机会标记
        ]

        matches = []
        used_pattern = None

        # 尝试每种模式
        for pattern in patterns:
            current_matches = list(re.finditer(pattern, full_log, re.IGNORECASE))
            if current_matches:
                matches = current_matches
                used_pattern = pattern
                print(f"✅ 使用模式找到 {len(current_matches)} 个分析结果标记: {pattern}")
                break

        if not matches:
            print("⚠️ 未找到标准分析结果标记")
            print("🔍 尝试智能提取...")

            # 智能提取：查找包含项目信息的内容
            lines = full_log.split('\n')
            potential_starts = []

            for i, line in enumerate(lines):
                line_lower = line.lower().strip()
                # 查找可能的分析结果开始位置
                if any(keyword in line_lower for keyword in [
                    'project name:', 'project 1:', '项目名称:', '项目1:',
                    'company:', '公司:', 'project description:', '项目描述:',
                    'business opportunity', '商机', '分析结果', 'analysis result'
                ]):
                    potential_starts.append(i)
                    print(f"🎯 找到潜在结果起始位置 {i}: {line.strip()[:50]}...")

            if potential_starts:
                # 使用最后一个潜在开始位置
                start_line = potential_starts[-1]
                # 向前查找更好的起始点
                for j in range(max(0, start_line - 10), start_line):
                    if any(marker in lines[j] for marker in ['----------', '===', '***']):
                        start_line = j
                        break

                content = '\n'.join(lines[start_line:]).strip()
                if len(content) > 50:
                    print(f"✅ 智能提取成功，内容长度: {len(content)} 字符")
                    orchestrator_file = logs_dir / f"analysis_result_{timestamp}.txt"
                    with open(orchestrator_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"📁 智能分析结果已保存到: {orchestrator_file}")
                    return content

            # 按照统一处理规则，直接报错，不使用备用方案
            print("❌ 所有提取方法都失败，按照统一处理规则直接报错")
            raise ValueError("无法从日志中提取到有效的分析结果，请检查分析过程是否正常完成")

        # 获取最后一个匹配的位置
        last_match = matches[-1]
        start_pos = last_match.start()  # 包含标记本身

        print(f"找到分析结果标记，位置: {start_pos}，使用模式: {used_pattern}")

        # 提取从标记开始到文件结尾的所有内容
        content = full_log[start_pos:].strip()

        # 保存提取的内容
        if content and len(content) > 50:  # 确保有实际内容
            orchestrator_file = logs_dir / f"analysis_result_{timestamp}.txt"
            with open(orchestrator_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 智能分析结果已保存到: {orchestrator_file}")
            print(f"📊 提取内容长度: {len(content)} 字符")
            return content
        else:
            print("❌ 提取的内容为空或过短，按照统一处理规则直接报错")
            raise ValueError("提取的分析结果内容不足，可能分析未正常完成")

    except Exception as e:
        print(f"❌ 提取智能分析结果时出错: {e}")
        # 按照统一处理规则，直接抛出错误
        raise ValueError(f"提取智能分析结果失败: {str(e)}") from e


def cleanup_all():
    """Run cleanup functions safely."""
    global _cleanup_done
    if _cleanup_done:
        return
    _cleanup_done = True

    try:
        # Basic cleanup - just suppress remaining warnings
        pass
    except Exception as e:
        print(f"Error during cleanup: {e}")


atexit.register(cleanup_all)


async def cleanup_mcp_tools(tools):
    """Cleanup MCP tools and their subprocesses safely."""
    if not tools:
        return

    # Simple approach - just let the tools cleanup naturally
    try:
        await asyncio.sleep(0.1)  # Give tools a moment to cleanup
    except Exception:
        pass


async def initialize_mcp_tools_with_retry(tavily_server_params, max_retries=3):
    """初始化MCP工具，带重试机制 - 优化版本"""
    import os  # 确保os模块可用

    for attempt in range(max_retries):
        try:
            print(f"初始化Tavily MCP工具 (尝试 {attempt + 1}/{max_retries})...")

            # 设置更长的超时时间和环境变量
            env = tavily_server_params.env.copy() if tavily_server_params.env else {}
            env.update({
                "TAVILY_API_KEY": os.getenv("TAVILY_API_KEY"),
                "HTTPX_TIMEOUT": "600",  # 增加到10分钟HTTP超时
                "AIOHTTP_TIMEOUT": "600",  # 增加到10分钟异步HTTP超时
                "REQUEST_TIMEOUT": "600",  # 增加到10分钟请求超时
                "CONNECT_TIMEOUT": "120",  # 连接超时2分钟
                "READ_TIMEOUT": "600"  # 读取超时10分钟
            })

            # 创建新的参数对象
            enhanced_params = StdioServerParams(
                command=tavily_server_params.command,
                args=tavily_server_params.args,
                env=env
            )

            tools = await asyncio.wait_for(
                mcp_server_tools(enhanced_params),
                timeout=300.0  # 增加到5分钟超时，确保MCP工具初始化成功
            )
            print("✅ Tavily MCP工具初始化成功")
            return tools
        except asyncio.TimeoutError:
            print(f"⚠️ MCP工具初始化超时 (尝试 {attempt + 1})")
            if attempt < max_retries - 1:
                print("等待30秒后重试...")
                await asyncio.sleep(30)  # 增加等待时间到30秒，给系统更多恢复时间
        except Exception as e:
            print(f"⚠️ MCP工具初始化失败: {str(e)} (尝试 {attempt + 1})")
            if attempt < max_retries - 1:
                print("等待30秒后重试...")
                await asyncio.sleep(30)  # 增加等待时间到30秒，给系统更多恢复时间

    print("❌ MCP工具初始化最终失败")
    return None


def get_model_client(model_name="grok-4-0709"):
    """获取通用模型客户端，支持多个AI提供商"""
    import os  # 确保os模块在函数开始时就可用

    # 加载环境变量
    load_dotenv(dotenv_path="MCP.env")

    # 定义所有支持的模型配置
    model_configs = {
        # xAI Grok系列模型 (通过xAI API) - 已优化上下文长度
        "grok-4-0709": {
            "provider": "xai",
            "api_key_env": "GROK_API_KEY",
            "base_url": "https://api.x.ai/v1",
            "actual_model_name": "grok-4-0709",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "xAI Grok-4-0709，最强推理模型"
        },
        "grok-4": {
            "provider": "xai",
            "api_key_env": "GROK_API_KEY",
            "base_url": "https://api.x.ai/v1",
            "actual_model_name": "grok-4",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "xAI Grok-4，平衡性能模型"
        },
        "grok-3-mini": {
            "provider": "xai",
            "api_key_env": "GROK_API_KEY",
            "base_url": "https://api.x.ai/v1",
            "actual_model_name": "grok-3-mini",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "xAI Grok-3-Mini，快速响应模型"
        },

        # ChatGPT系列模型 (通过OpenRouter) - 已优化上下文长度
        "openai/o3": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "openai/o3",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "OpenAI O3模型，最新一代推理模型"
        },
        "openai/gpt-4.1": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "openai/gpt-4.1",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "GPT-4.1模型，增强版本"
        },
        "openai/gpt-4.1-mini": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "openai/gpt-4.1-mini",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "GPT-4.1 Mini模型，轻量版本"
        },
        "openai/gpt-4.1-nano": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "openai/gpt-4.1-nano",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "GPT-4.1 Nano模型，超轻量版本"
        },
        "openai/gpt-4o-mini-search-preview": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "openai/gpt-4o-mini-search-preview",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "GPT-4o Mini搜索预览版"
        },
        "openai/gpt-4o-search-preview": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "openai/gpt-4o-search-preview",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "GPT-4o搜索预览版"
        },
        "openai/gpt-4o": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "openai/gpt-4o",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "GPT-4o模型，多模态能力"
        },
        "openai/gpt-4o-mini": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "openai/gpt-4o-mini",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "GPT-4o Mini模型，轻量多模态"
        },

        # Claude系列模型 (通过OpenRouter) - 已优化上下文长度
        "anthropic/claude-opus-4": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "anthropic/claude-opus-4",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Claude Opus 4，最强推理和分析能力"
        },
        "anthropic/claude-sonnet-4": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "anthropic/claude-sonnet-4",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Claude Sonnet 4，平衡性能和效率"
        },
        "anthropic/claude-3.7-sonnet": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "anthropic/claude-3.7-sonnet",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Claude 3.7 Sonnet，增强版本"
        },
        "anthropic/claude-3.7-sonnet:beta": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "anthropic/claude-3.7-sonnet:beta",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Claude 3.7 Sonnet Beta版本"
        },
        "anthropic/claude-3.7-sonnet:thinking": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "anthropic/claude-3.7-sonnet:thinking",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Claude 3.7 Sonnet思维链版本"
        },
        "anthropic/claude-3.5-haiku:beta": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "anthropic/claude-3.5-haiku:beta",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Claude 3.5 Haiku Beta版本"
        },
        "anthropic/claude-3.5-haiku": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "anthropic/claude-3.5-haiku",
            "context_window": 1000000,  # 增加到100万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Claude 3.5 Haiku，快速响应版本"
        },

        # Llama系列模型 (通过OpenRouter) - 已优化上下文长度
        "meta-llama/llama-4-maverick": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "meta-llama/llama-4-maverick",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Llama 4 Maverick，创新版本"
        },
        "meta-llama/llama-4-scout": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "meta-llama/llama-4-scout",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Llama 4 Scout，探索版本"
        },
        "thedrummer/anubis-70b-v1.1": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "thedrummer/anubis-70b-v1.1",
            "context_window": 200000,  # 增加到20万tokens
            "max_tokens": 8192,  # 增加到8K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Anubis 70B v1.1，专业分析模型"
        },
        "nvidia/llama-3.1-nemotron-ultra-253b-v1:free": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "NVIDIA Nemotron Ultra 253B免费版"
        },
        "nvidia/llama-3.1-nemotron-ultra-253b-v1": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "nvidia/llama-3.1-nemotron-ultra-253b-v1",
            "context_window": 500000,  # 增加到50万tokens
            "max_tokens": 16384,  # 增加到16K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "NVIDIA Nemotron Ultra 253B完整版"
        },

        # Gemini系列模型 (通过OpenRouter) - 已优化上下文长度
        "google/gemini-2.5-flash-lite": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "google/gemini-2.5-flash-lite",
            "context_window": 2000000,  # 保持200万tokens（Gemini支持超长上下文）
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Gemini 2.5 Flash Lite，轻量快速版本"
        },
        "google/gemini-2.5-flash-lite-preview-06-17": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "google/gemini-2.5-flash-lite-preview-06-17",
            "context_window": 2000000,  # 保持200万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Gemini 2.5 Flash Lite预览版"
        },
        "google/gemini-2.5-flash": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "google/gemini-2.5-flash",
            "context_window": 2000000,  # 保持200万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Gemini 2.5 Flash，快速多模态模型"
        },
        "google/gemini-2.5-pro": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "google/gemini-2.5-pro",
            "context_window": 2000000,  # 保持200万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Gemini 2.5 Pro，专业级多模态模型"
        },
        "google/gemini-2.5-pro-preview": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "actual_model_name": "google/gemini-2.5-pro-preview",
            "context_window": 2000000,  # 保持200万tokens
            "max_tokens": 32768,  # 增加到32K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Gemini 2.5 Pro预览版"
        },

        # Qwen系列模型 (阿里云API) - 已优化上下文长度
        "qwen-max": {
            "provider": "aliyun",
            "api_key_env": "ALIYUN_API_KEY",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "actual_model_name": "qwen-max",
            "context_window": 200000,  # 增加到20万tokens
            "max_tokens": 8192,  # 增加到8K tokens
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "阿里云通义千问最强版本，多模态能力强"
        },
        "qwen-plus": {
            "provider": "aliyun",
            "api_key_env": "ALIYUN_API_KEY",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "actual_model_name": "qwen-plus",
            "context_window": 200000,  # 增加到20万tokens
            "max_tokens": 8192,  # 增加到8K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "阿里云通义千问增强版本，平衡性能和成本"
        },
        "qwen-turbo": {
            "provider": "aliyun",
            "api_key_env": "ALIYUN_API_KEY",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "actual_model_name": "qwen-turbo",
            "context_window": 100000,  # 增加到10万tokens
            "max_tokens": 4096,  # 增加到4K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "阿里云通义千问快速版本，响应速度快，成本低"
        },
        "qwq-plus": {
            "provider": "aliyun",
            "api_key_env": "ALIYUN_API_KEY",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "actual_model_name": "qwq-plus",
            "context_window": 200000,  # 增加到20万tokens
            "max_tokens": 8192,  # 增加到8K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "QwQ Plus，增强推理能力"
        },
        "qwen3-235b-a22b-thinking-2507": {
            "provider": "aliyun",
            "api_key_env": "ALIYUN_API_KEY",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "actual_model_name": "qwen3-235b-a22b-thinking-2507",
            "context_window": 200000,  # 增加到20万tokens
            "max_tokens": 8192,  # 增加到8K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Qwen3 235B思维链版本"
        },
        "qwen3-235b-a22b-instruct-2507": {
            "provider": "aliyun",
            "api_key_env": "ALIYUN_API_KEY",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "actual_model_name": "qwen3-235b-a22b-instruct-2507",
            "context_window": 200000,  # 增加到20万tokens
            "max_tokens": 8192,  # 增加到8K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Qwen3 235B指令版本"
        },
        "qwen3-32b": {
            "provider": "aliyun",
            "api_key_env": "ALIYUN_API_KEY",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "actual_model_name": "qwen3-32b",
            "context_window": 200000,  # 增加到20万tokens
            "max_tokens": 8192,  # 增加到8K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "Qwen3 32B标准版本"
        },

        # DeepSeek系列模型 (DeepSeek API) - 已优化上下文长度
        "deepseek-chat": {
            "provider": "deepseek",
            "api_key_env": "DEEPSEEK_API_KEY",
            "base_url": "https://api.deepseek.com/v1",
            "actual_model_name": "deepseek-chat",
            "context_window": 200000,  # 增加到20万tokens
            "max_tokens": 8192,  # 增加到8K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "DeepSeek对话模型，强大的中文理解和推理能力"
        },
        "deepseek-reasoner": {
            "provider": "deepseek",
            "api_key_env": "DEEPSEEK_API_KEY",
            "base_url": "https://api.deepseek.com/v1",
            "actual_model_name": "deepseek-reasoner",
            "context_window": 200000,  # 增加到20万tokens
            "max_tokens": 8192,  # 增加到8K tokens
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,
            "multiple_system_messages": True,
            "supports_agents": True,
            "description": "DeepSeek推理模型，专业的逻辑推理和分析能力"
        }
    }

    # 获取模型配置
    if model_name not in model_configs:
        model_name = "openai/gpt-4o"  # 默认使用GPT-4o

    config = model_configs[model_name]

    # 获取对应的API密钥
    api_key = os.getenv(config["api_key_env"])
    if not api_key:
        raise ValueError(f"{config['api_key_env']}环境变量是必需的。请在MCP.env文件中设置它。")

    # 定义模型信息，根据模型特性调整
    model_info = ModelInfo(
        family=config["provider"],
        name=model_name,
        context_window=config["context_window"],
        max_tokens=config["max_tokens"],
        vision=config["vision"],
        function_calling=config["function_calling"],
        json_output=config["json_output"],
        structured_output=config["structured_output"],
        multiple_system_messages=config["multiple_system_messages"]
    )

    # 临时抑制所有模型相关警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")

        # 设置环境变量进一步抑制警告
        old_env = os.environ.get("TOKENIZERS_PARALLELISM")
        os.environ["TOKENIZERS_PARALLELISM"] = "false"

        try:
            # 确定实际使用的模型名称
            actual_model = config.get("actual_model_name", model_name)

            # 创建客户端参数
            client_kwargs = {
                "model": actual_model,
                "model_info": model_info,
                "api_key": api_key,
                "base_url": config["base_url"]
            }

            # 创建并返回模型客户端
            client = OpenAIChatCompletionClient(**client_kwargs)

            if actual_model != model_name:
                print(f"✅ 已初始化 {config['provider'].upper()} 模型: {model_name} -> {actual_model}")
            else:
                print(f"✅ 已初始化 {config['provider'].upper()} 模型: {model_name}")

            return client
        finally:
            # 恢复环境变量
            if old_env is not None:
                os.environ["TOKENIZERS_PARALLELISM"] = old_env
            elif "TOKENIZERS_PARALLELISM" in os.environ:
                del os.environ["TOKENIZERS_PARALLELISM"]


# 保持向后兼容性的别名
def get_grok_model_client(model_name="grok-4-0709"):
    """获取Grok模型客户端 (向后兼容)"""
    return get_model_client(model_name)


def get_model_config(model_name="openai/gpt-4o"):
    """获取模型配置信息"""
    import os
    load_dotenv(dotenv_path="MCP.env")

    # 重复模型配置定义（为了独立性）
    model_configs = {
        # ChatGPT系列
        "openai/o3": {"supports_agents": True, "provider": "openrouter"},
        "openai/gpt-4.1": {"supports_agents": True, "provider": "openrouter"},
        "openai/gpt-4.1-mini": {"supports_agents": True, "provider": "openrouter"},
        "openai/gpt-4.1-nano": {"supports_agents": True, "provider": "openrouter"},
        "openai/gpt-4o-mini-search-preview": {"supports_agents": True, "provider": "openrouter"},
        "openai/gpt-4o-search-preview": {"supports_agents": True, "provider": "openrouter"},
        "openai/gpt-4o": {"supports_agents": True, "provider": "openrouter"},
        "openai/gpt-4o-mini": {"supports_agents": True, "provider": "openrouter"},

        # Claude系列
        "anthropic/claude-opus-4": {"supports_agents": True, "provider": "openrouter"},
        "anthropic/claude-sonnet-4": {"supports_agents": True, "provider": "openrouter"},
        "anthropic/claude-3.7-sonnet": {"supports_agents": True, "provider": "openrouter"},
        "anthropic/claude-3.7-sonnet:beta": {"supports_agents": True, "provider": "openrouter"},
        "anthropic/claude-3.7-sonnet:thinking": {"supports_agents": True, "provider": "openrouter"},
        "anthropic/claude-3.5-haiku:beta": {"supports_agents": True, "provider": "openrouter"},
        "anthropic/claude-3.5-haiku": {"supports_agents": True, "provider": "openrouter"},

        # Llama系列
        "meta-llama/llama-4-maverick": {"supports_agents": True, "provider": "openrouter"},
        "meta-llama/llama-4-scout": {"supports_agents": True, "provider": "openrouter"},
        "thedrummer/anubis-70b-v1.1": {"supports_agents": True, "provider": "openrouter"},
        "nvidia/llama-3.1-nemotron-ultra-253b-v1:free": {"supports_agents": True, "provider": "openrouter"},
        "nvidia/llama-3.1-nemotron-ultra-253b-v1": {"supports_agents": True, "provider": "openrouter"},

        # Gemini系列
        "google/gemini-2.5-flash-lite": {"supports_agents": True, "provider": "openrouter"},
        "google/gemini-2.5-flash-lite-preview-06-17": {"supports_agents": True, "provider": "openrouter"},
        "google/gemini-2.5-flash": {"supports_agents": True, "provider": "openrouter"},
        "google/gemini-2.5-pro": {"supports_agents": True, "provider": "openrouter"},
        "google/gemini-2.5-pro-preview": {"supports_agents": True, "provider": "openrouter"},

        # Qwen系列
        "qwen-max": {"supports_agents": True, "provider": "aliyun"},
        "qwen-plus": {"supports_agents": True, "provider": "aliyun"},
        "qwen-turbo": {"supports_agents": True, "provider": "aliyun"},
        "qwq-plus": {"supports_agents": True, "provider": "aliyun"},
        "qwen3-235b-a22b-thinking-2507": {"supports_agents": True, "provider": "aliyun"},
        "qwen3-235b-a22b-instruct-2507": {"supports_agents": True, "provider": "aliyun"},
        "qwen3-32b": {"supports_agents": True, "provider": "aliyun"},

        # DeepSeek系列
        "deepseek-chat": {"supports_agents": True, "provider": "deepseek"},
        "deepseek-reasoner": {"supports_agents": True, "provider": "deepseek"}
    }

    return model_configs.get(model_name, {"supports_agents": True, "provider": "unknown"})


def log_prompt_details(stage: str, prompt_content: str, agent_name: str = ""):
    """
    记录详细的提示词信息到日志中
    """
    print(f"\n{'=' * 80}")
    print(f"📝 {stage} - 提示词详情")
    if agent_name:
        print(f"🤖 代理: {agent_name}")
    print(f"📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'=' * 80}")
    print(f"📋 提示词内容:")
    print(f"{'-' * 80}")
    print(prompt_content)
    print(f"{'-' * 80}")
    print(f"📊 提示词长度: {len(prompt_content)} 字符")
    print(f"{'=' * 80}\n")


def log_execution_result(stage: str, result_content: str, agent_name: str = ""):
    """
    记录详细的执行结果到日志中
    """
    print(f"\n{'=' * 80}")
    print(f"📊 {stage} - 执行结果详情")
    if agent_name:
        print(f"🤖 代理: {agent_name}")
    print(f"📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'=' * 80}")
    print(f"📋 执行结果:")
    print(f"{'-' * 80}")
    print(result_content)
    print(f"{'-' * 80}")
    print(f"📊 结果长度: {len(result_content)} 字符")
    print(f"{'=' * 80}\n")


def get_current_real_time_log():
    """获取当前的实时日志内容"""
    global _current_real_time_log, _log_update_lock, _analysis_status
    try:
        with _log_update_lock:
            if _analysis_status["running"]:
                # 构建状态信息头部
                current_time = datetime.now().strftime('%H:%M:%S')
                status_header = f"""
🤖 智能商机分析系统 - 实时日志
⏰ 更新时间: {current_time}
🔄 分析状态: {_analysis_status['stage']}
📊 当前进度: {_analysis_status['progress']}
{'='*60}

"""
                # 返回完整的实时日志内容
                return status_header + (_current_real_time_log or "")
            else:
                if _current_real_time_log:
                    # 分析完成后显示完整日志
                    current_time = datetime.now().strftime('%H:%M:%S')
                    completion_header = f"""
🤖 智能商机分析系统 - 实时日志
⏰ 更新时间: {current_time}
✅ 分析状态: 已完成
📋 日志内容: 显示完整执行记录
{'='*60}

"""
                    return completion_header + _current_real_time_log
                else:
                    return """
🤖 智能商机分析系统 - 实时日志
⏰ 状态: 等待分析开始
💡 提示: 填写产品信息后点击"开始商机分析"按钮
📺 实时日志将在分析开始后自动显示
"""
    except Exception as e:
        return f"❌ 日志获取失败: {str(e)}"


def clear_real_time_log():
    """清空实时日志"""
    global _current_real_time_log, _log_update_lock, _analysis_status
    try:
        with _log_update_lock:
            _current_real_time_log = ""
            _analysis_status = {"running": False, "progress": "", "stage": ""}
    except Exception:
        pass


def update_analysis_status(stage: str, progress: str, running: bool = True):
    """更新分析状态"""
    global _analysis_status, _log_update_lock
    try:
        with _log_update_lock:
            _analysis_status = {
                "running": running,
                "stage": stage,
                "progress": progress
            }
    except Exception:
        pass


# 移除故障排除建议函数 - 按照统一处理规则，遇到问题直接报错


# 移除配置优化函数 - 按照统一处理规则，使用标准配置，不进行特殊优化


# 移除备用分析函数 - 按照统一处理规则，不使用备用分析方案


async def analyze_business_opportunities(product_name: str, product_details: str,
                                         countries: str, time_period: str,
                                         opportunities_count: int, use_custom_templates: bool,
                                         template_manager: PromptTemplateManager,
                                         selected_model: str = "grok-4-0709",
                                         progress_callback=None) -> Tuple[str, str]:
    """
    主要的分析函数，返回 (结果, 完整日志)
    """
    tools = None

    # 创建计时管理器
    timing_manager = TimingManager()
    timing_manager.start_total()

    # 创建静默日志记录器和日志捕获器
    original_stdout = sys.stdout
    original_stderr = sys.stderr

    # 设置静默日志记录器来抑制警告
    silent_stdout = SilentLogger(original_stdout)
    silent_stderr = SilentLogger(original_stderr)

    # 创建日志捕获器
    log_capture = LogCapture()

    # 同时设置静默和捕获
    sys.stdout = log_capture
    sys.stderr = silent_stderr  # stderr使用静默记录器

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    print(f"开始商机分析任务 - {timestamp}")
    print(f"产品: {product_name}")
    print(f"目标地区: {countries}")
    print(f"时间段: {time_period}")
    print(f"期望商机数量: {opportunities_count}")
    print("=" * 50)

    try:
        if progress_callback:
            progress_callback("🔧 初始化系统...")

        print(f"使用自定义模板: {'是' if use_custom_templates else '否'}")
        print(f"选择模型: {selected_model}")

        # 抑制所有警告信息
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")

            # 获取模型客户端和配置
            model_client = get_model_client(selected_model)
            model_config = get_model_config(selected_model)

        # 获取Tavily API密钥
        TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")
        if not TAVILY_API_KEY:
            raise ValueError("TAVILY_API_KEY环境变量是必需的。请在MCP.env文件中设置它。")

        TAVILY_MCP_SERVER = StdioServerParams(
            command="npx",
            args=["-y", "tavily-mcp@0.2.0"],
            env={"TAVILY_API_KEY": TAVILY_API_KEY},
        )

        try:
            if progress_callback:
                progress_callback("🔍 启动销售专家分析...")

            # 开始第一阶段计时
            timing_manager.start_stage1()
            print(f"⏱️ 第一阶段开始 - {datetime.now().strftime('%H:%M:%S')}")

            tools = await initialize_mcp_tools_with_retry(TAVILY_MCP_SERVER)
            if not tools:
                print("⚠️ 无法初始化搜索工具，将跳过销售专家分析")
                expert_output = "由于网络问题，跳过了销售专家分析阶段。"
            else:
                # 获取销售专家提示词
                if use_custom_templates:
                    sales_expert_prompt = template_manager.format_sales_expert_prompt(
                        product_name, product_details, countries, time_period, opportunities_count)
                else:
                    sales_expert_prompt = template_manager.format_sales_expert_prompt(
                        product_name, product_details, countries, time_period, opportunities_count)

                # 验证销售专家提示词
                if not sales_expert_prompt or len(sales_expert_prompt.strip()) < 100:
                    print(f"❌ 销售专家提示词异常: 长度={len(sales_expert_prompt) if sales_expert_prompt else 0}")
                    raise ValueError("销售专家提示词为空或过短，请检查模板配置")
                else:
                    print(f"✅ 销售专家提示词正常: 长度={len(sales_expert_prompt)}")

                # 记录销售专家提示词到日志
                log_prompt_details("第一阶段 - 销售专家分析", sales_expert_prompt, "销售专家 (Sales Expert)")

                # 创建销售专家Agent - 使用正确的系统提示词
                print(f"🤖 使用Agent模式进行销售专家分析 ({selected_model})")
                sales_expert = AssistantAgent(
                    name="sales_expert",
                    model_client=model_client,
                    system_message=sales_expert_prompt,
                    tools=tools,
                    description="专业的销售专家，能够分析产品信息并推荐相关的网站和搜索关键词"
                )

                # 验证模板使用是否符合统一处理规则
                validation_result = template_manager.validate_template_usage("sales_expert", sales_expert_prompt,
                                                                             "user_prompt_only")
                if not validation_result["is_valid"]:
                    print("❌ 模板验证失败:")
                    for error in validation_result["errors"]:
                        print(f"   - {error}")
                    raise ValueError("提示词模板使用不符合统一处理规则")

                if validation_result["warnings"]:
                    print("⚠️ 模板验证警告:")
                    for warning in validation_result["warnings"]:
                        print(f"   - {warning}")

                # 记录销售专家任务到日志
                log_prompt_details("第一阶段 - 销售专家任务", sales_expert_prompt, "销售专家任务")
                log_prompt_details("第一阶段 - 完整提示词模板", sales_expert_prompt, "销售专家完整模板")

                print(f"🎯 销售专家分析任务: {sales_expert_prompt[:100]}...")
                print("📋 使用完整的提示词模板，确保流程可控")
                print("✅ 模板验证通过，流程完全可控")

                # 执行销售专家分析
                expert_output = ""
                max_retries = 5  # 增加重试次数到5次，提高成功率
                retry_count = 0
                expert_timeout = 600.0  # 10分钟超时

                print(f"🎯 销售专家分析配置:")
                print(f"   - 最大重试次数: {max_retries}")
                print(f"   - 单次超时时间: {expert_timeout / 60:.1f}分钟")
                print(f"   - 总计最大时间: {(expert_timeout * max_retries) / 60:.1f}分钟")

                while retry_count < max_retries:
                    try:
                        print(
                            f"🔄 尝试销售专家分析 (第 {retry_count + 1}/{max_retries} 次，超时{expert_timeout / 60:.1f}分钟)...")

                        # 创建带有更长超时的取消令牌
                        cancellation_token = CancellationToken()

                        expert_result = await asyncio.wait_for(
                            sales_expert.run(
                                task=sales_expert_prompt,
                                cancellation_token=cancellation_token
                            ),
                            timeout=expert_timeout  # 使用配置的超时时间，确保搜索有充足时间完成
                        )

                        # 提取销售专家的分析结果
                        raw_content = ""

                        if hasattr(expert_result, 'messages') and expert_result.messages:
                            # 获取所有消息内容
                            all_content = []
                            tool_results = []
                            final_analysis = []

                            print(f"📊 销售专家返回了 {len(expert_result.messages)} 条消息")

                            for i, message in enumerate(expert_result.messages):
                                if hasattr(message, 'content'):
                                    content = str(message.content)
                                    if content.strip():
                                        all_content.append(content)

                                        # 跳过工具调用和错误信息，只显示有用的内容
                                        if (content.startswith('[FunctionCall') or
                                                content.startswith('[FunctionExecutionResult') or
                                                'Error: Extra data' in content or
                                                'Timed out while waiting' in content):
                                            if 'Error:' in content or 'Timed out' in content:
                                                error_msg = content.split('Error:')[1].strip()[
                                                            :50] if 'Error:' in content else content[:50]
                                                print(f"⚠️ 工具调用错误 (消息 {i + 1}): {error_msg}...")
                                            continue

                                        # 查找真正的分析结果
                                        if (len(content) > 100 and
                                                ('website' in content.lower() or
                                                 'recommendation' in content.lower() or
                                                 'analysis' in content.lower() or
                                                 'http' in content.lower() or
                                                 'industry' in content.lower())):
                                            final_analysis.append(content)
                                            print(f"✅ 找到分析结果 (消息 {i + 1}): {content[:80]}...")
                                        elif len(content) > 50:
                                            print(f"📝 其他内容 (消息 {i + 1}): {content[:80]}...")

                            print(f"📊 消息处理结果: 总计{len(all_content)}条消息, 找到{len(final_analysis)}个分析结果")

                            # 选择最佳内容 - 简化逻辑
                            if final_analysis:
                                raw_content = final_analysis[-1]  # 使用最后一个分析结果
                                print("✅ 使用销售专家分析结果")
                            else:
                                # 如果没有找到分析结果，查找其他有用内容
                                useful_content = []
                                for content in all_content:
                                    if (len(content.strip()) > 50 and
                                            not content.startswith('[Function') and
                                            'Error:' not in content):
                                        useful_content.append(content)

                                if useful_content:
                                    raw_content = useful_content[-1]
                                    print("⚠️ 使用其他可用内容")
                                else:
                                    print("❌ 没有找到有效的分析结果")
                        else:
                            print("❌ 销售专家没有返回任何消息")

                        if raw_content:
                            # 检查内容是否包含工具错误、超时错误
                            if ("Timed out while waiting for response" in raw_content or
                                    "Error: Extra data" in raw_content or
                                    "validation errors for GeneratedModel" in raw_content or
                                    "Waited 5.0 seconds" in raw_content):

                                if "include_domains" in raw_content and "exclude_domains" in raw_content:
                                    print("⚠️ 检测到tavily-search工具参数格式错误，准备重试...")
                                else:
                                    print("⚠️ 检测到工具调用错误，准备重试...")
                                retry_count += 1
                                await asyncio.sleep(10)  # 增加等待时间到10秒
                                continue

                            # 清理内容，移除技术性错误信息
                            cleaned_content = clean_expert_output(raw_content)

                            if cleaned_content and len(cleaned_content.strip()) > 20:
                                expert_output = cleaned_content
                                print("✅ 销售专家分析成功完成")

                                # 记录完整结果到日志
                                log_execution_result("第一阶段 - 销售专家分析结果", expert_output,
                                                     "销售专家 (Sales Expert)")

                                # 显示简洁的结果预览
                                preview = expert_output[:200].replace('\n', ' ')
                                print(f"📋 结果预览: {preview}...")

                                # 显示完整的销售专家结果到日志中
                                print("=" * 80)
                                print("📋 销售专家完整分析结果:")
                                print("=" * 80)
                                print(expert_output)
                                print("=" * 80)
                                break
                            else:
                                print("⚠️ 清理后内容不足，准备重试...")
                                if cleaned_content:
                                    print(f"⚠️ 清理后内容: '{cleaned_content[:100]}...'")
                                else:
                                    print("⚠️ 清理后内容为空")

                        print("⚠️ 销售专家返回无效结果，准备重试...")
                        retry_count += 1
                        await asyncio.sleep(3)  # 等待3秒后重试

                    except asyncio.TimeoutError:
                        retry_count += 1
                        print(
                            f"⚠️ 销售专家分析超时 (第 {retry_count}/{max_retries} 次，超时时间{expert_timeout / 60:.1f}分钟)")
                        if retry_count < max_retries:
                            wait_time = 30 + (retry_count * 15)  # 递增等待时间：30s, 45s, 60s, 75s
                            print(f"🔄 等待{wait_time}秒后重试，给系统更多恢复时间...")
                            await asyncio.sleep(wait_time)
                        else:
                            print(f"❌ 销售专家分析最终超时，已尝试{max_retries}次")
                    except Exception as e:
                        retry_count += 1
                        error_msg = str(e)
                        print(f"⚠️ 销售专家分析出错: {error_msg} (第 {retry_count}/{max_retries} 次)")
                        if retry_count < max_retries:
                            wait_time = 20 + (retry_count * 10)  # 递增等待时间：20s, 30s, 40s, 50s
                            print(f"🔄 等待{wait_time}秒后重试...")
                            await asyncio.sleep(wait_time)
                        else:
                            print(f"❌ 销售专家分析最终失败，已尝试{max_retries}次")

                # 按照统一处理规则，如果销售专家分析失败，直接报错
                if not expert_output or not expert_output.strip():
                    raise ValueError("销售专家分析失败，无法获取有效的分析结果。请检查网络连接、API配置或重新尝试。")

                # 显示清晰的销售专家分析结果
                if expert_output and expert_output.strip():
                    expert_output = ensure_utf8_encoding(expert_output)
                    print("\n" + "=" * 80)
                    print("📋 销售专家分析结果")
                    print("=" * 80)
                    print(expert_output)
                    print("=" * 80)
                else:
                    print("⚠️ 销售专家分析结果为空")

        finally:
            # Clean up MCP tools
            if tools:
                await cleanup_mcp_tools(tools)

        # 结束第一阶段计时
        timing_manager.end_stage1()
        stage1_duration = timing_manager.get_stage1_duration()
        print(f"✅ 第一阶段完成 - 耗时: {timing_manager.format_duration(stage1_duration)}")

        # 第二阶段：智能深度分析
        if progress_callback:
            progress_callback("🚀 开始深度商机挖掘...")

        # 开始第二阶段计时
        timing_manager.start_stage2()
        print(f"⏱️ 第二阶段开始 - {datetime.now().strftime('%H:%M:%S')}")

        # 严格验证expert_output - 按照统一处理规则，不使用默认内容
        if not expert_output or expert_output.strip() == "":
            raise ValueError("销售专家输出为空，无法进行后续分析。请检查第一阶段是否正常完成。")

        # 初始化智能分析引擎 - 使用标准配置
        print(f"🤖 启动智能分析引擎进行深度分析 (llama-4-maverick)")

        # 抑制技术相关警告
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            # 使用标准配置，不进行特殊优化
            m1_agent = MagenticOne(client=model_client)

        # 获取智能分析提示词 - 统一处理规则
        # 统一使用format_magentic_one_prompt方法，确保从prompt_templates.json加载模板
        m1_task = template_manager.format_magentic_one_prompt(
            product_name, product_details, countries, time_period,
            opportunities_count, expert_output)

        # 验证MagenticOne用户提示词使用是否符合统一处理规则
        magentic_validation = template_manager.validate_template_usage("magentic_one", m1_task, "user_prompt_only")
        if not magentic_validation["is_valid"]:
            print("❌ MagenticOne用户提示词验证失败:")
            for error in magentic_validation["errors"]:
                print(f"   - {error}")
            print("⚠️ 继续执行但可能影响分析质量")

        if magentic_validation["warnings"]:
            print("⚠️ MagenticOne用户提示词验证警告:")
            for warning in magentic_validation["warnings"]:
                print(f"   - {warning}")

        # 记录MagenticOne用户提示词到日志
        log_prompt_details("第二阶段 - MagenticOne用户提示词", m1_task, "MagenticOne用户提示词")

        print("🎯 开始智能深度分析 - 使用纯用户提示词模式")
        print("📋 MagenticOne直接使用用户提示词，无系统提示词干扰")
        print("✅ 用户完全控制分析流程和输出格式")

        if progress_callback:
            if opportunities_count <= 10:
                progress_callback("⚙️ 执行深度分析中...")
            elif opportunities_count <= 50:
                progress_callback(f"⚙️ 执行中等深度分析中 ({opportunities_count}个商机)...")
            else:
                progress_callback(f"⚙️ 执行广度分析中 ({opportunities_count}个商机，预计需要较长时间)...")

        try:
            # 根据商机数量动态调整超时时间，最短20分钟
            base_timeout = 1200.0  # 基础20分钟
            additional_timeout = opportunities_count * 60.0  # 每个商机额外1分钟
            max_timeout = 3600.0  # 最大60分钟

            dynamic_timeout = max(min(base_timeout + additional_timeout, max_timeout), 1200.0)  # 确保最短20分钟

            print(f"⏱️ 设置分析超时时间: {dynamic_timeout / 60:.1f}分钟 (基于{opportunities_count}个商机)")

            if opportunities_count > 20:
                print(
                    f"📊 大量商机分析提示: 正在分析{opportunities_count}个商机，这可能需要{dynamic_timeout / 60:.1f}分钟")
                print("💡 建议: 如需更快速度，可以减少商机数量到10-20个")

            # 执行智能深度分析
            print("🤖 使用智能分析引擎执行深度分析...")
            # 添加超时控制
            result = await asyncio.wait_for(
                Console(m1_agent.run_stream(task=m1_task)),
                timeout=dynamic_timeout
            )
            print("✅ 智能分析完成")
        except asyncio.TimeoutError as e:
            # 按照统一处理规则，直接报错，不使用备用方案
            print(f"❌ 智能分析超时 ({dynamic_timeout / 60:.1f}分钟)")
            raise TimeoutError(
                f"智能分析超时，请检查网络连接或减少商机数量。超时时间: {dynamic_timeout / 60:.1f}分钟") from e
        except Exception as e:
            # 按照统一处理规则，直接报错，不使用备用方案
            error_str = str(e)
            print(f"❌ 智能分析出错: {error_str}")
            raise RuntimeError(f"智能分析失败: {error_str}") from e

        if progress_callback:
            progress_callback("✅ 分析完成，处理结果...")

        # 按照统一处理规则，验证分析结果 - 不使用降级处理
        if not result:
            raise ValueError("智能分析未返回有效结果，请检查分析过程或重新尝试")

        # 结束第二阶段计时
        timing_manager.end_stage2()
        stage2_duration = timing_manager.get_stage2_duration()
        print(f"✅ 第二阶段完成 - 耗时: {timing_manager.format_duration(stage2_duration)}")
        print("智能商机挖掘完成！")

    except Exception as e:
        # 确保在错误情况下也结束计时
        if 'timing_manager' in locals():
            timing_manager.end_total()
            timing_summary = timing_manager.get_timing_summary()
            print(timing_summary)

        error_msg = f"执行过程中出现错误: {str(e)}\n{traceback.format_exc()}"
        print(f"错误详情: {error_msg}")
        # 在错误情况下也返回当前的日志
        full_log = log_capture.get_full_log() if 'log_capture' in locals() else ""
        return error_msg, full_log
    finally:
        # 结束总计时
        timing_manager.end_total()

        # 恢复原始输出
        sys.stdout = original_stdout
        sys.stderr = original_stderr

        print("正在处理和保存日志...")

        # 获取计时统计
        timing_summary = timing_manager.get_timing_summary()
        print(timing_summary)

        # 确保获取完整日志
        log_capture.flush()
        full_log = log_capture.get_full_log()

        # 添加分析完成标记和计时统计
        completion_marker = f"\n=== 分析完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n"
        completion_marker += timing_summary
        full_log += completion_marker

        print(f"完整日志长度: {len(full_log)} 字符")

        # 保存日志并提取结果
        orchestrator_result = save_logs_to_file(full_log, timestamp)

        # 严格验证结果提取 - 按照统一处理规则，不使用备用方案
        if not orchestrator_result or len(str(orchestrator_result).strip()) < 50:
            print("❌ 结果提取失败，按照统一处理规则直接报错")
            raise ValueError("无法从分析日志中提取到有效结果，请检查分析过程是否正常完成")

        # 返回有效结果
        print(f"✅ 返回有效结果，长度: {len(str(orchestrator_result))} 字符")
        return (orchestrator_result, full_log)


def get_model_series_choices():
    """获取按系列分组的模型选择"""
    return {
        "ChatGPT系列": [
            ("🚀 O3", "openai/o3"),
            ("🧠 GPT-4.1", "openai/gpt-4.1"),
            ("⚡ GPT-4.1 Mini", "openai/gpt-4.1-mini"),
            ("🏃 GPT-4.1 Nano", "openai/gpt-4.1-nano"),
            ("🔍 GPT-4o 搜索版", "openai/gpt-4o-search-preview"),
            ("🔍 GPT-4o Mini 搜索版", "openai/gpt-4o-mini-search-preview"),
            ("👁️ GPT-4o", "openai/gpt-4o"),
            ("📱 GPT-4o Mini", "openai/gpt-4o-mini")
        ],
        "Claude系列": [
            ("🎯 Claude Opus 4", "anthropic/claude-opus-4"),
            ("⚖️ Claude Sonnet 4", "anthropic/claude-sonnet-4"),
            ("🔧 Claude 3.7 Sonnet", "anthropic/claude-3.7-sonnet"),
            ("🧪 Claude 3.7 Sonnet Beta", "anthropic/claude-3.7-sonnet:beta"),
            ("🤔 Claude 3.7 Sonnet 思维链", "anthropic/claude-3.7-sonnet:thinking"),
            ("🧪 Claude 3.5 Haiku Beta", "anthropic/claude-3.5-haiku:beta"),
            ("⚡ Claude 3.5 Haiku", "anthropic/claude-3.5-haiku")
        ],
        "Llama系列": [
            ("🚀 Llama 4 Maverick", "meta-llama/llama-4-maverick"),
            ("🔍 Llama 4 Scout", "meta-llama/llama-4-scout"),
            ("🔥 Anubis 70B v1.1", "thedrummer/anubis-70b-v1.1"),
            ("🆓 NVIDIA Nemotron Ultra 免费版", "nvidia/llama-3.1-nemotron-ultra-253b-v1:free"),
            ("💎 NVIDIA Nemotron Ultra", "nvidia/llama-3.1-nemotron-ultra-253b-v1")
        ],
        "Gemini系列": [
            ("⚡ Gemini 2.5 Flash Lite", "google/gemini-2.5-flash-lite"),
            ("🧪 Gemini 2.5 Flash Lite 预览", "google/gemini-2.5-flash-lite-preview-06-17"),
            ("🔥 Gemini 2.5 Flash", "google/gemini-2.5-flash"),
            ("🎯 Gemini 2.5 Pro", "google/gemini-2.5-pro"),
            ("🧪 Gemini 2.5 Pro 预览", "google/gemini-2.5-pro-preview")
        ],
        "Qwen系列": [
            ("🌟 Qwen Max", "qwen-max"),
            ("⚖️ Qwen Plus", "qwen-plus"),
            ("🏃 Qwen Turbo", "qwen-turbo"),
            ("🤔 QwQ Plus", "qwq-plus"),
            ("🧠 Qwen3 235B 思维链", "qwen3-235b-a22b-thinking-2507"),
            ("📝 Qwen3 235B 指令版", "qwen3-235b-a22b-instruct-2507"),
            ("⚡ Qwen3 32B", "qwen3-32b")
        ],
        "DeepSeek系列": [
            ("💬 DeepSeek Chat", "deepseek-chat"),
            ("🤔 DeepSeek Reasoner", "deepseek-reasoner")
        ]
    }


def create_gradio_interface():
    """创建Gradio界面"""

    # 初始化模板管理器
    template_manager = PromptTemplateManager()

    # 默认值
    default_product_name = "化学品合成设备"
    default_product_details = "应用于生物制药的微流"
    default_countries = "美国"
    default_time_period = "2025年7月"
    default_opportunities_count = 5

    def generate_default_templates(product_name, product_details, countries, time_period, opportunities_count):
        """生成默认模板（填入用户数据）"""
        # 使用format方法确保从prompt_templates.json加载模板
        sales_template = template_manager.format_sales_expert_prompt(
            product_name=product_name or "{product_name}",
            product_details=product_details or "{product_details}",
            countries=countries or "{countries}"
        )

        # 对于MagenticOne模板，需要提供一个临时的expert_output
        temp_expert_output = "临时专家输出 - 用于模板预览"
        magentic_template = template_manager.format_magentic_one_prompt(
            product_name=product_name or "{product_name}",
            product_details=product_details or "{product_details}",
            countries=countries or "{countries}",
            time_period=time_period or "{time_period}",
            opportunities_count=opportunities_count or "{opportunities_count}",
            expert_output=temp_expert_output
        )

        return sales_template, magentic_template

    def save_custom_templates(sales_template, magentic_template):
        """保存自定义模板"""
        try:
            template_manager.update_sales_expert_template(sales_template)
            template_manager.update_magentic_one_template(magentic_template)
            return "✅ 自定义模板已保存！"
        except Exception as e:
            return f"❌ 保存失败: {str(e)}"

    def reset_templates():
        """重置模板为默认值"""
        try:
            template_manager.reset_to_default()
            return (template_manager.sales_expert_template,
                    template_manager.magentic_one_template,
                    "✅ 模板已重置为默认值！")
        except Exception as e:
            return ("", "", f"❌ 重置失败: {str(e)}")

    def load_current_templates():
        """加载当前保存的模板"""
        return (template_manager.sales_expert_template,
                template_manager.magentic_one_template,
                "✅ 已加载当前保存的模板")

    def run_analysis(product_name, product_details, countries, time_period, opportunities_count,
                     use_custom_templates, selected_model, progress=gr.Progress()):
        """运行分析的包装函数 - 简化版本"""

        # 输入验证
        if not product_name or not product_name.strip():
            error_msg = "❌ 错误：产品名称不能为空"
            return error_msg, error_msg

        if not product_details or not product_details.strip():
            error_msg = "❌ 错误：产品详情不能为空"
            return error_msg, error_msg

        if not countries or not countries.strip():
            error_msg = "❌ 错误：目标国家/地区不能为空"
            return error_msg, error_msg

        if not time_period or not time_period.strip():
            error_msg = "❌ 错误：检索时间段不能为空"
            return error_msg, error_msg

        if not isinstance(opportunities_count, int) or opportunities_count < 1 or opportunities_count > 100:
            error_msg = "❌ 错误：期望商机数量必须是1-100之间的整数"
            return error_msg, error_msg

        # 清空实时日志并设置初始状态
        clear_real_time_log()
        update_analysis_status("初始化", "准备开始分析...", True)

        # 抑制所有警告
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")

            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                def update_progress(message):
                    progress(0.5, desc=message)
                    # 同时更新分析状态
                    if "销售专家" in message:
                        update_analysis_status("第一阶段", message, True)
                    elif "深度分析" in message:
                        update_analysis_status("第二阶段", message, True)
                    else:
                        update_analysis_status("执行中", message, True)

                try:
                    print("🚀 开始执行商机分析...")
                    update_analysis_status("启动", "正在初始化分析引擎...", True)

                    result, full_log = loop.run_until_complete(
                        analyze_business_opportunities(
                            product_name, product_details, countries,
                            time_period, opportunities_count, use_custom_templates,
                            template_manager, selected_model, update_progress
                        )
                    )

                    print(
                        f"📊 分析完成 - 结果长度: {len(str(result)) if result else 0}, 日志长度: {len(str(full_log)) if full_log else 0}")

                    # 按照统一处理规则，不使用备用方案，直接返回结果
                    # 如果结果包含错误，让错误正常传播，不进行额外处理

                    print("✅ 返回分析结果")
                    # 标记分析完成
                    update_analysis_status("完成", "分析已完成", False)
                    return result, full_log

                finally:
                    loop.close()

            except Exception as e:
                error_msg = f"❌ 执行错误: {str(e)}"
                detailed_error = f"详细错误信息:\n{traceback.format_exc()}"
                update_analysis_status("错误", f"分析失败: {str(e)}", False)
                return error_msg, detailed_error

    # 创建界面
    with gr.Blocks(
            title="智能商机分析系统",
            theme=gr.themes.Soft(),
            css="""
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #e0e0e0;
            margin-bottom: 30px;
        }
        .input-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .result-section {
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .template-section {
            background: #fff5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ff6b6b;
        }
        """
    ) as interface:

        # 标题和说明
        with gr.Row():
            gr.HTML("""
            <div class="header">
                <h1>🤖 智能商机分析系统</h1>
                <p>基于先进AI技术进行深度市场分析，智能发现潜在商业机会</p>
            </div>
            """)

        # 模型信息显示（固定为llama-4-maverick）
        with gr.Row():
            with gr.Column():
                # 固定显示当前使用的模型
                model_info = gr.HTML("""
                <div style="padding: 15px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #4CAF50;">
                    <h4>🤖 当前使用模型</h4>
                    <p><strong>llama-4-maverick</strong> - 高性能智能分析模型</p>
                    <p>专为商机分析和市场洞察优化</p>
                </div>
                """)

                # 隐藏的模型选择器（固定值，用于后端处理）
                model_selector = gr.Dropdown(
                    label="模型选择",
                    choices=[("llama-4-maverick", "meta-llama/llama-4-maverick")],
                    value="meta-llama/llama-4-maverick",
                    visible=False
                )

        # 网络状态显示
        def get_network_status():
            """获取网络状态信息 - 简化版本"""
            return f"""
            <div style="padding: 15px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #4CAF50; margin-bottom: 15px;">
                <h4>🌍 系统状态</h4>
                <p><strong>✅ 系统运行正常</strong></p>
                <p style="font-size: 12px; color: #666;">
                    💡 智能商机分析系统已就绪，可以开始分析
                </p>
            </div>
            """

        with gr.Row():
            network_status = gr.HTML(get_network_status())

        # 移除模型选择相关的事件绑定（已固定为llama-4-maverick）

        # 主界面 Tab
        with gr.Tabs():
            # 主分析界面
            with gr.Tab("🚀 商机分析"):
                # 输入区域
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.HTML("<h3>📝 产品信息输入</h3>")

                        product_name = gr.Textbox(
                            label="产品名称",
                            value=default_product_name,
                            placeholder="请输入产品名称",
                            lines=1
                        )

                        product_details = gr.Textbox(
                            label="产品详细信息",
                            value=default_product_details,
                            placeholder="请输入产品的详细描述、技术参数、应用场景等",
                            lines=4
                        )

                        with gr.Row():
                            countries = gr.Textbox(
                                label="目标国家/地区",
                                value=default_countries,
                                placeholder="多个国家用逗号分隔",
                                scale=2
                            )

                            opportunities_count = gr.Number(
                                label="期望商机数量 (1-100)",
                                value=default_opportunities_count,
                                minimum=1,
                                maximum=100,
                                step=1,
                                scale=1,
                                info="建议：3-10个商机分析速度较快，超过20个可能需要较长时间"
                            )

                        time_period = gr.Textbox(
                            label="检索时间段",
                            value=default_time_period,
                            placeholder="例如：2025年1月-7月"
                        )

                        # 模板选择
                        with gr.Row():
                            use_custom_templates = gr.Checkbox(
                                label="🎨 使用自定义提示词模板",
                                value=False,
                                info="勾选后将使用您在'提示词管理'中定义的自定义模板"
                            )

                        analyze_btn = gr.Button(
                            "🚀 开始商机分析",
                            variant="primary",
                            size="lg"
                        )

                # 结果区域
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>📊 分析结果</h3>")
                        result_output = gr.Textbox(
                            label="商机分析结果",
                            placeholder="分析结果将在这里显示...",
                            lines=20,
                            max_lines=30,
                            show_copy_button=True,
                            interactive=False
                        )

                # 实时日志区域
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>📺 实时日志</h3>")
                        gr.HTML("""
                        <div style="padding: 10px; background: #f0f8ff; border-radius: 5px; margin-bottom: 10px;">
                            <p><strong>💡 实时日志说明：</strong></p>
                            <p>• 自动实时更新分析过程中的进度和详细信息</p>
                            <p>• 包含完整的提示词内容，方便您查看和优化</p>
                            <p>• 每2秒自动刷新，无需手动操作</p>
                            <p>• 显示完整的分析日志，不会截断重要信息</p>
                        </div>
                        """)

                        real_time_log = gr.Textbox(
                            label="实时执行日志",
                            placeholder="分析开始后，实时日志将在这里显示...",
                            lines=15,
                            max_lines=25,
                            show_copy_button=True,
                            interactive=False
                        )

                        # 自动刷新状态显示
                        with gr.Row():
                            auto_refresh_status = gr.Textbox(
                                label="自动刷新状态",
                                value="✅ 自动刷新已启用 (每2秒)",
                                interactive=False,
                                lines=1,
                                max_lines=1
                            )

                # 完整日志区域
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>📋 完整日志</h3>")
                        full_log_output = gr.Textbox(
                            label="完整系统日志",
                            placeholder="完整日志将在这里显示...",
                            lines=15,
                            max_lines=25,
                            show_copy_button=True,
                            interactive=False
                        )

                # 实时日志更新函数
                def update_real_time_log_display():
                    """更新实时日志显示"""
                    return get_current_real_time_log()

                def update_auto_refresh_status():
                    """更新自动刷新状态显示"""
                    global _analysis_status
                    if _analysis_status.get("running", False):
                        return "🔄 自动刷新运行中 (每2秒) - 分析进行中"
                    else:
                        return "✅ 自动刷新已启用 (每2秒) - 等待分析"

                # 绑定事件 - 修复返回值数量
                analyze_btn.click(
                    fn=run_analysis,
                    inputs=[product_name, product_details, countries, time_period,
                            opportunities_count, use_custom_templates, model_selector],
                    outputs=[result_output, full_log_output],  # 只有两个输出
                    show_progress=True
                )

                # 创建定时器用于自动更新实时日志
                timer = gr.Timer(value=2.0)  # 每2秒触发一次
                timer.tick(
                    fn=lambda: (update_real_time_log_display(), update_auto_refresh_status()),
                    outputs=[real_time_log, auto_refresh_status]
                )

            # 提示词管理界面
            with gr.Tab("🎨 提示词管理"):
                gr.HTML("<h3>🔧 自定义提示词模板</h3>")

                with gr.Row():
                    gr.HTML("""
                    <div style="padding: 15px; background: #e8f4fd; border-radius: 8px; margin-bottom: 20px;">
                        <h4>📋 使用说明</h4>
                        <ul>
                            <li><strong>生成模板：</strong>填入产品信息后点击"生成默认模板"</li>
                            <li><strong>自定义编辑：</strong>基于生成的模板进行个性化修改</li>
                            <li><strong>保存使用：</strong>保存后在主界面勾选"使用自定义提示词模板"</li>
                        </ul>
                    </div>
                    """)

                # 输入区域用于生成模板
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h4>📝 产品信息（用于生成默认模板）</h4>")

                        template_product_name = gr.Textbox(
                            label="产品名称",
                            value=default_product_name,
                            placeholder="请输入产品名称"
                        )

                        template_product_details = gr.Textbox(
                            label="产品详情",
                            value=default_product_details,
                            placeholder="请输入产品详情",
                            lines=3
                        )

                        template_countries = gr.Textbox(
                            label="目标国家",
                            value=default_countries,
                            placeholder="目标国家/地区"
                        )

                        template_time_period = gr.Textbox(
                            label="时间段",
                            value=default_time_period,
                            placeholder="时间段"
                        )

                        template_opportunities_count = gr.Number(
                            label="商机数量 (1-100)",
                            value=default_opportunities_count,
                            minimum=1,
                            maximum=100,
                            info="用于生成模板预览"
                        )

                # 模板操作按钮
                with gr.Row():
                    generate_btn = gr.Button("🔄 生成默认模板", variant="secondary")
                    load_btn = gr.Button("📂 加载已保存模板", variant="secondary")
                    reset_btn = gr.Button("↩️ 重置为系统默认", variant="secondary")

                # 模板编辑区域
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h4>🤖 销售专家模板 (llama-4-maverick)</h4>")
                        sales_expert_template = gr.Textbox(
                            label="销售专家提示词模板",
                            value=template_manager.sales_expert_template,
                            lines=15,
                            max_lines=25,
                            placeholder="销售专家提示词模板...",
                            show_copy_button=True
                        )

                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h4>🎯 智能分析模板 (llama-4-maverick)</h4>")
                        magentic_one_template = gr.Textbox(
                            label="智能分析提示词模板",
                            value=template_manager.magentic_one_template,
                            lines=20,
                            max_lines=30,
                            placeholder="智能分析提示词模板...",
                            show_copy_button=True
                        )

                # 保存按钮和状态
                with gr.Row():
                    save_btn = gr.Button("💾 保存自定义模板", variant="primary", size="lg")

                with gr.Row():
                    save_status = gr.Textbox(
                        label="操作状态",
                        placeholder="操作状态将在这里显示...",
                        interactive=False,
                        lines=1
                    )

                # 绑定模板管理事件
                generate_btn.click(
                    fn=generate_default_templates,
                    inputs=[template_product_name, template_product_details,
                            template_countries, template_time_period, template_opportunities_count],
                    outputs=[sales_expert_template, magentic_one_template]
                )

                load_btn.click(
                    fn=load_current_templates,
                    outputs=[sales_expert_template, magentic_one_template, save_status]
                )

                reset_btn.click(
                    fn=reset_templates,
                    outputs=[sales_expert_template, magentic_one_template, save_status]
                )

                save_btn.click(
                    fn=save_custom_templates,
                    inputs=[sales_expert_template, magentic_one_template],
                    outputs=[save_status]
                )

            # 帮助文档
            with gr.Tab("📖 帮助文档"):
                gr.HTML("""
                <div style="padding: 20px; line-height: 1.6;">
                    <h2>🤖 智能商机分析系统使用指南</h2>

                    <h3>📋 基本使用</h3>
                    <ol>
                        <li><strong>填写产品信息:</strong> 输入产品名称和详细描述</li>
                        <li><strong>设置参数:</strong> 选择目标国家、时间段和商机数量</li>
                        <li><strong>选择模型:</strong> 根据需求选择合适的AI模型</li>
                        <li><strong>开始分析:</strong> 点击"开始商机分析"按钮</li>
                        <li><strong>查看结果:</strong> 在结果区域查看商机，在日志区域查看详细过程</li>
                    </ol>

                    <h3>🎨 自定义提示词</h3>
                    <p>在"提示词管理"标签页中可以自定义分析模板：</p>
                    <ol>
                        <li>填写产品信息，点击"生成默认模板"</li>
                        <li>编辑模板内容</li>
                        <li>保存模板</li>
                        <li>在主界面勾选"使用自定义提示词模板"</li>
                    </ol>

                    <h3>🔧 两阶段分析</h3>
                    <ul>
                        <li><strong>第一阶段:</strong> 销售专家分析产品特点和市场定位</li>
                        <li><strong>第二阶段:</strong> llama-4-maverick智能引擎深度挖掘潜在商业机会</li>
                    </ul>

                    <h3>🤖 关于llama-4-maverick</h3>
                    <ul>
                        <li><strong>专业模型:</strong> 专为商机分析和市场洞察优化</li>
                        <li><strong>高效分析:</strong> 快速识别潜在商业机会</li>
                        <li><strong>精准推理:</strong> 基于大规模商业数据训练</li>
                    </ul>
                </div>
                """)

    return interface


def main():
    """主函数"""
    # 全局抑制所有警告
    warnings.simplefilter("ignore")

    # 设置环境变量抑制各种警告
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ["PYTHONWARNINGS"] = "ignore"
    os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"

    # 检查环境变量
    load_dotenv(dotenv_path="MCP.env")

    if not os.getenv("GROK_API_KEY"):
        print("⚠️  警告: 未找到 GROK_API_KEY 环境变量")
        print("请在 MCP.env 文件中设置 GROK_API_KEY")
        return

    if not os.getenv("TAVILY_API_KEY"):
        print("⚠️  警告: 未找到 TAVILY_API_KEY 环境变量")
        print("请在 MCP.env 文件中设置 TAVILY_API_KEY")
        return

    try:
        # 获取网络信息
        network_info = get_network_info()

        # 创建并启动界面
        interface = create_gradio_interface()

        print("🚀 启动智能商机分析系统...")
        print("🤖 基于AI模型进行深度市场分析")
        print("🎨 支持自定义提示词模板管理")
        print("🌐 正在启动Web界面，请稍候...")

        # 显示所有可用的访问URL
        print("\n" + "=" * 60)
        print("🌍 可用访问地址")
        print("=" * 60)
        print(f"🏠 本地访问: http://127.0.0.1:7860")
        print(f"🌐 局域网访问: http://{network_info['local_ip']}:7860")
        if network_info['is_network_accessible']:
            print(f"📡 服务器内网: {network_info['network_url']}")
        print("🌍 公网访问: Gradio将自动生成分享链接")
        print("🔧 云服务器访问: http://[您的公网IP]:7860")
        print("=" * 60)

        # 启动界面 - 支持多种访问方式
        app, local_url, share_url = interface.launch(
            server_name="0.0.0.0",  # 允许外部访问
            server_port=7860,  # 指定端口
            share=True,  # 启用公网分享（Gradio隧道）
            inbrowser=True,  # 自动打开浏览器
            prevent_thread_lock=False,  # 防止线程锁定
            debug=False,  # 关闭调试模式以提高稳定性
            show_error=True,  # 显示错误信息
            quiet=False  # 显示访问URL信息
        )

        # 显示所有可用的访问URL
        display_all_access_urls(network_info, share_url)
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        print("请检查环境配置和依赖安装")

        # 如果启动失败，尝试仅本地访问
        try:
            print("\n🔄 尝试仅本地访问模式...")
            interface = create_gradio_interface()
            interface.launch(
                server_name="127.0.0.1",
                server_port=7860,
                share=False,
                inbrowser=True,
                quiet=False  # 显示访问URL信息
            )
        except Exception as e2:
            print(f"❌ 本地访问模式也失败: {str(e2)}")
            print("请检查端口7860是否被占用或重新安装依赖")


if __name__ == "__main__":
    main()



