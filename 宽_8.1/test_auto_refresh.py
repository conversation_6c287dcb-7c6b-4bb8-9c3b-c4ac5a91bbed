#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动刷新功能
"""
import gradio as gr
import threading
import time
from datetime import datetime

# 全局变量用于实时日志显示
_current_real_time_log = ""
_log_update_lock = threading.Lock()
_analysis_status = {"running": False, "progress": "", "stage": ""}

def update_analysis_status(stage: str, progress: str, running: bool = True):
    """更新分析状态"""
    global _analysis_status, _log_update_lock, _current_real_time_log
    try:
        with _log_update_lock:
            _analysis_status = {
                "running": running,
                "stage": stage,
                "progress": progress,
                "last_update": datetime.now().strftime('%H:%M:%S')
            }
            
            # 同时在实时日志中添加状态更新信息
            if running:
                status_log = f"[{_analysis_status['last_update']}] 🔄 {stage}: {progress}\n"
                # 将状态更新添加到实时日志
                _current_real_time_log += status_log
    except Exception:
        pass

def get_current_real_time_log():
    """获取当前的实时日志内容"""
    global _current_real_time_log, _log_update_lock, _analysis_status
    try:
        with _log_update_lock:
            if _analysis_status["running"]:
                # 构建状态信息头部
                current_time = datetime.now().strftime('%H:%M:%S')
                status_header = f"""
🤖 智能商机分析系统 - 实时日志
⏰ 更新时间: {current_time}
🔄 分析状态: {_analysis_status['stage']}
📊 当前进度: {_analysis_status['progress']}
{'='*60}

"""
                # 返回完整的实时日志内容
                return status_header + (_current_real_time_log or "")
            else:
                if _current_real_time_log:
                    # 分析完成后显示完整日志
                    current_time = datetime.now().strftime('%H:%M:%S')
                    completion_header = f"""
🤖 智能商机分析系统 - 实时日志
⏰ 更新时间: {current_time}
✅ 分析状态: 已完成
📋 日志内容: 显示完整执行记录
{'='*60}

"""
                    return completion_header + _current_real_time_log
                else:
                    return """
🤖 智能商机分析系统 - 实时日志
⏰ 状态: 等待分析开始
💡 提示: 点击"开始测试分析"按钮
📺 实时日志将在分析开始后自动显示
"""
    except Exception as e:
        return f"❌ 日志获取失败: {str(e)}"

def clear_real_time_log():
    """清空实时日志"""
    global _current_real_time_log, _log_update_lock, _analysis_status
    try:
        with _log_update_lock:
            _current_real_time_log = ""
            _analysis_status = {"running": False, "progress": "", "stage": ""}
    except Exception:
        pass

def simulate_analysis():
    """模拟分析过程"""
    global _current_real_time_log, _log_update_lock
    
    # 清空日志
    clear_real_time_log()
    
    # 模拟分析步骤
    steps = [
        ("初始化", "正在准备分析环境...", 2),
        ("环境配置", "正在加载配置和模型...", 3),
        ("第一阶段", "正在启动销售专家分析...", 4),
        ("第一阶段", "销售专家分析中 (第1次尝试)...", 5),
        ("第一阶段", "销售专家工具初始化完成，开始分析...", 3),
        ("第一阶段完成", "销售专家分析完成，耗时2.3分钟", 2),
        ("第二阶段", "正在启动智能深度分析引擎...", 3),
        ("第二阶段", "智能分析执行中 (预计5.0分钟)...", 8),
        ("第二阶段完成", "智能深度分析完成，正在整理结果...", 2),
        ("分析完成", "所有分析已完成，总耗时15.3分钟", 1)
    ]
    
    for i, (stage, progress, duration) in enumerate(steps):
        # 更新状态
        running = i < len(steps) - 1
        update_analysis_status(stage, progress, running)
        
        # 添加详细日志
        with _log_update_lock:
            detail_log = f"📝 步骤 {i+1}/{len(steps)}: {progress}\n"
            if "分析中" in progress:
                detail_log += "   🔍 正在搜索相关信息...\n"
                detail_log += "   📊 正在处理数据...\n"
            elif "完成" in progress:
                detail_log += "   ✅ 步骤执行成功\n"
            
            _current_real_time_log += detail_log
        
        # 等待指定时间
        time.sleep(duration)
    
    return "✅ 模拟分析完成！"

def create_test_interface():
    """创建测试界面"""
    with gr.Blocks(title="自动刷新测试", theme=gr.themes.Soft()) as interface:
        gr.HTML("<h1>🤖 自动刷新功能测试</h1>")
        
        with gr.Row():
            with gr.Column():
                start_btn = gr.Button("🚀 开始测试分析", variant="primary", size="lg")
                clear_btn = gr.Button("🧹 清空日志", variant="secondary")
        
        with gr.Row():
            with gr.Column():
                gr.HTML("<h3>📺 实时日志</h3>")
                gr.HTML("""
                <div style="padding: 10px; background: #f0f8ff; border-radius: 5px; margin-bottom: 10px;">
                    <p><strong>💡 实时日志说明：</strong></p>
                    <p>• 自动实时更新分析过程中的进度和详细信息</p>
                    <p>• 每2秒自动刷新，无需手动操作</p>
                    <p>• 显示完整的分析日志，不会截断重要信息</p>
                </div>
                """)

                real_time_log = gr.Textbox(
                    label="实时执行日志",
                    placeholder="点击开始测试分析后，实时日志将在这里显示...",
                    lines=15,
                    max_lines=25,
                    show_copy_button=True,
                    interactive=False
                )

                # 自动刷新状态显示
                with gr.Row():
                    auto_refresh_status = gr.Textbox(
                        label="自动刷新状态",
                        value="✅ 自动刷新已启用 (每2秒)",
                        interactive=False,
                        lines=1,
                        max_lines=1
                    )
        
        with gr.Row():
            with gr.Column():
                result_output = gr.Textbox(
                    label="分析结果",
                    placeholder="分析结果将在这里显示...",
                    lines=5,
                    interactive=False
                )
        
        # 实时日志更新函数
        def update_real_time_log_display():
            """更新实时日志显示"""
            return get_current_real_time_log()

        def update_auto_refresh_status():
            """更新自动刷新状态显示"""
            global _analysis_status
            if _analysis_status.get("running", False):
                return "🔄 自动刷新运行中 (每2秒) - 分析进行中"
            else:
                return "✅ 自动刷新已启用 (每2秒) - 等待分析"
        
        def clear_log():
            """清空日志"""
            clear_real_time_log()
            return "", "日志已清空"

        # 绑定事件
        start_btn.click(
            fn=simulate_analysis,
            outputs=[result_output]
        )
        
        clear_btn.click(
            fn=clear_log,
            outputs=[real_time_log, result_output]
        )

        # 创建定时器用于自动更新实时日志
        timer = gr.Timer(value=2.0)  # 每2秒触发一次
        timer.tick(
            fn=lambda: (update_real_time_log_display(), update_auto_refresh_status()),
            outputs=[real_time_log, auto_refresh_status]
        )
    
    return interface

if __name__ == "__main__":
    print("🚀 启动自动刷新功能测试...")
    interface = create_test_interface()
    interface.launch(
        server_name="127.0.0.1",
        server_port=7862,
        share=False,
        inbrowser=True
    )
